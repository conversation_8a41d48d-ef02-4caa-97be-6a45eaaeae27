import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { P } from "@/components/typography";
import { FileText, ArrowRight } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";
import Link from "next/link";

interface ServiceRequirementsProps {
  serviceRequirements: CaseFileDashboardData["serviceRequirements"];
  caseFile: CaseFileDashboardData["caseFile"];
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    serviceName?: string;
    location?: string;
    frequency?: string;
    duration?: string;
    startDate?: string;
    endDate?: string;
    viewDetails?: string;
  };
}

/**
 * Service Requirements component for case file dashboard
 * Displays detailed service information including schedule, location, and progress
 */
export function ServiceRequirements({
  serviceRequirements,
  caseFile,
  caseFileId,
  lang,
  dictionary,
}: ServiceRequirementsProps) {
  // Default translations
  const defaultDictionary = {
    title: "Service Requirements",
    description: "Service information from request",
    serviceName: "Service",
    location: "Location",
    frequency: "Frequency",
    duration: "Duration",
    startDate: "Start Date",
    endDate: "End Date",
    viewDetails: "View Details",
  };

  const t = {
    ...defaultDictionary,
    ...dictionary,
  };

  // Get service information from case file request
  const request = caseFile.request;
  const hasRequest = !!request;
  const hasService = hasRequest && !!request.service;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          {t.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {hasRequest ? (
          <div className="space-y-4">
            {/* Service Information - Summary Only */}
            {hasService && (
              <div className="space-y-2">
                <P className="font-semibold text-lg">{request.service?.name}</P>
                {request.service?.description && (
                  <P className="text-sm text-muted-foreground line-clamp-2">
                    {request.service.description}
                  </P>
                )}
              </div>
            )}

            {/* Key Details - Simplified */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              {/* Start Date */}
              {request.start_date && (
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">{t.startDate}</span>
                  <div className="font-medium">
                    {new Date(request.start_date).toLocaleDateString()}
                  </div>
                </div>
              )}

              {/* Duration */}
              {request.duration && (
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">{t.duration}</span>
                  <div className="font-medium">{request.duration} min</div>
                </div>
              )}

              {/* Frequency */}
              {request.periodicity && (
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">{t.frequency}</span>
                  <div className="font-medium capitalize">{request.periodicity}</div>
                </div>
              )}

              {/* Location */}
              {request.location && (
                <div className="space-y-1">
                  <span className="text-muted-foreground text-xs">{t.location}</span>
                  <div className="font-medium">{request.location.name}</div>
                </div>
              )}
            </div>

            {/* Service Stats */}
            <div className="grid grid-cols-2 gap-3 text-sm">
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="font-semibold text-lg">
                  {serviceRequirements.upcomingAppointments}
                </div>
                <div className="text-xs text-muted-foreground">Upcoming</div>
              </div>
              <div className="text-center p-3 bg-muted/50 rounded-lg">
                <div className="font-semibold text-lg">{serviceRequirements.pendingDocuments}</div>
                <div className="text-xs text-muted-foreground">Pending Docs</div>
              </div>
            </div>

            {/* Request Reference */}
            {request.reference_number && (
              <div className="text-xs text-muted-foreground">
                <span>Request: {request.reference_number}</span>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-6">
            <FileText className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <P className="text-muted-foreground">No service request found</P>
            <P className="text-xs text-muted-foreground mt-1">
              Service information will appear here once a request is linked
            </P>
          </div>
        )}

        {/* Action Button - Link to Service Request Tab */}
        <Button variant="outline" size="sm" className="w-full" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=service-request`}>
            {t.viewDetails || "View Full Request"}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
