"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { P } from "@/components/typography";
import {
  FileText,
  Calendar,
  Clock,
  MapPin,
  Hash,
  Target,
  AlertCircle
} from "lucide-react";
import { motion } from "framer-motion";
import { CaseFileWithDetails } from "../../../lib/case-file/types";

interface CaseFileServiceRequestProps {
  caseFile: CaseFileWithDetails;
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    serviceDetails: string;
    requestDetails: string;
    locationDetails: string;
    contactDetails: string;
    scheduleDetails: string;
    requirements: string;
    noRequest: string;
    noRequestDescription: string;
    serviceName: string;
    serviceDescription: string;
    requestNumber: string;
    requestTitle: string;
    requestDescription: string;
    startDate: string;
    endDate: string;
    duration: string;
    frequency: string;
    location: string;
    address: string;
    contactPerson: string;
    phone: string;
    email: string;
    status: string;
    priority: string;
    objectives: string;
    specialRequirements: string;
  };
}

/**
 * Service Request component for case file
 * Displays comprehensive service request information and requirements
 */
export function CaseFileServiceRequest({
  caseFile,
  caseFileId: _caseFileId,
  lang: _lang,
  dictionary,
}: CaseFileServiceRequestProps) {
  // Default translations
  const defaultDictionary = {
    title: "Service Request",
    description: "Complete service request information and requirements",
    serviceDetails: "Service Details",
    requestDetails: "Request Details",
    locationDetails: "Location Details",
    contactDetails: "Contact Details",
    scheduleDetails: "Schedule Details",
    requirements: "Requirements",
    noRequest: "No Service Request",
    noRequestDescription: "No service request information is available for this case file.",
    serviceName: "Service Name",
    serviceDescription: "Service Description",
    requestNumber: "Request Number",
    requestTitle: "Request Title",
    requestDescription: "Request Description",
    startDate: "Start Date",
    endDate: "End Date",
    duration: "Duration",
    frequency: "Frequency",
    location: "Location",
    address: "Address",
    contactPerson: "Contact Person",
    phone: "Phone",
    email: "Email",
    status: "Status",
    priority: "Priority",
    objectives: "Objectives",
    specialRequirements: "Special Requirements",
  };

  const t = {
    ...defaultDictionary,
    ...dictionary,
  };

  // Get request information from case file
  const request = caseFile.request;
  const hasRequest = !!request;

  if (!hasRequest) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              {t.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <P className="text-lg font-medium mb-2">{t.noRequest}</P>
              <P className="text-muted-foreground">{t.noRequestDescription}</P>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="space-y-6"
    >
      {/* Service Details */}
      {request.service && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              {t.serviceDetails}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <P className="font-semibold text-xl mb-2">{request.service.name}</P>
              {request.service.description && (
                <P className="text-muted-foreground">{request.service.description}</P>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Request Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t.requestDetails}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Request Number */}
          {request.reference_number && (
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Hash className="h-4 w-4" />
                {t.requestNumber}
              </div>
              <P className="font-medium">{request.reference_number}</P>
            </div>
          )}

          {/* Additional Request Information */}
          {request.frequency_count && (
            <div className="space-y-1">
              <P className="text-sm font-medium text-muted-foreground">Frequency Count</P>
              <P className="font-medium">{request.frequency_count}</P>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Schedule Details */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            {t.scheduleDetails}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Start Date */}
            {request.start_date && (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  {t.startDate}
                </div>
                <P className="font-medium">
                  {new Date(request.start_date).toLocaleDateString()}
                </P>
              </div>
            )}

            {/* End Date */}
            {request.end_date && (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  {t.endDate}
                </div>
                <P className="font-medium">
                  {new Date(request.end_date).toLocaleDateString()}
                </P>
              </div>
            )}

            {/* Duration */}
            {request.duration && (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  {t.duration}
                </div>
                <P className="font-medium">{request.duration} minutes</P>
              </div>
            )}

            {/* Frequency */}
            {request.periodicity && (
              <div className="space-y-1">
                <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  {t.frequency}
                </div>
                <P className="font-medium capitalize">{request.periodicity}</P>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Location Details */}
      {request.location && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              {t.locationDetails}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <P className="font-semibold text-lg mb-1">{request.location.name}</P>
              {request.location.address && (
                <P className="text-muted-foreground">{request.location.address}</P>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
}
