import { notFound } from "next/navigation";
import { getCaseFileDashboardData, getCaseFileContacts } from "../../actions";
import { getCaseFileAppointments } from "../../actions/appointments";
import { CaseFileDashboard } from "../../components";
import { CaseFileNavigation } from "../../components/navigation";
import { PageHeader } from "@/components/PageHeader";
import { Button } from "@/components/ui/button";
import { Settings, Users, Calendar } from "lucide-react";
import { CaseFileDocuments } from "./components/CaseFileDocuments";
import { CaseFileHistory } from "./components/CaseFileHistory";
import { CaseFileContacts } from "./components/CaseFileContacts";
import { CaseFileAppointments } from "./components/CaseFileAppointments";
import { getDictionary } from "@/lib/i18n/cache";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface ActiveCaseFilePageProps {
  params: Promise<{
    lang: string;
    id: string;
  }>;
}

/**
 * Active Case File Management Page
 * Central hub for managing ongoing family services
 */
export default async function ActiveCaseFilePage({ params }: ActiveCaseFilePageProps) {
  const { lang, id } = await params;

  // Get dictionary for translations
  const dictionary = await getDictionary();
  const t = dictionary.caseFile.active;

  // Get dashboard data using server action
  const dashboardResponse = await getCaseFileDashboardData(id);

  if (!dashboardResponse.success || !dashboardResponse.data) {
    notFound();
  }

  const { caseFile } = dashboardResponse.data;

  // Get case file contacts
  const contactsResponse = await getCaseFileContacts(id);
  const contacts = contactsResponse.success ? contactsResponse.data || [] : [];

  // Get case file appointments
  const appointmentsResponse = await getCaseFileAppointments(id);
  const appointments = appointmentsResponse.success ? appointmentsResponse.data || [] : [];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title={`${t.title} ${caseFile.case_number}`}
        description={t.description}
        actions={
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button variant="outline" size="sm">
              <Users className="h-4 w-4 mr-2" />
              Family
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              {t.dashboard.quickActions.schedule}
            </Button>
          </div>
        }
      />

      {/* Case File Navigation */}
      <CaseFileNavigation caseFileId={caseFile.id} currentStatus={caseFile.status} lang={lang} />

      {/* Tabbed Content */}
      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="dashboard">{t.tabs.dashboard}</TabsTrigger>
          <TabsTrigger value="documents">{t.tabs.documents}</TabsTrigger>
          <TabsTrigger value="contacts">{t.tabs.contacts}</TabsTrigger>
          <TabsTrigger value="appointments">{t.tabs.appointments}</TabsTrigger>
          <TabsTrigger value="history">{t.tabs.history}</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-6">
          <CaseFileDashboard data={dashboardResponse.data} lang={lang} dictionary={t.dashboard} />
        </TabsContent>

        <TabsContent value="documents" className="space-y-6">
          <CaseFileDocuments caseFileId={caseFile.id} lang={lang} dictionary={t.documents} />
        </TabsContent>

        <TabsContent value="contacts" className="space-y-6">
          <CaseFileContacts
            contacts={contacts}
            caseFileId={caseFile.id}
            lang={lang}
            dictionary={t.contacts}
          />
        </TabsContent>

        <TabsContent value="appointments" className="space-y-6">
          <CaseFileAppointments
            caseFileId={caseFile.id}
            appointments={appointments}
            lang={lang}
            dictionary={t.appointments}
          />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <CaseFileHistory caseFileId={caseFile.id} lang={lang} dictionary={t.history} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
