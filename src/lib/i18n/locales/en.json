{"common": {"appName": "Si Simple - Supervised Visitation Management Platform", "welcome": "Welcome to the Si Simple platform", "loading": "Loading...", "save": "Save", "create": "Create", "all": "All", "language": "Language", "french": "French", "english": "English", "help": "Help", "contactUs": "Contact Us", "error": "Error", "tryAgain": "Try Again", "clearFilters": "Clear", "goHome": "Go Home", "pageNotFound": "Page Not Found", "cancel": "Cancel", "search": "Search", "searchPlaceholder": "Search...", "clear": "Clear", "by": "by", "actions": "Actions", "saving": "Saving...", "remove": "Remove", "back": "Back", "goBack": "Go Back", "returnToHomepage": "Return to Homepage", "errorOccurred": "An error occurred", "errorMessage": "There was an error. Please try again.", "previous": "Previous", "next": "Next", "submit": "Submit", "submitting": "Submitting...", "errorId": "Error ID", "profile": "Profile", "account": "Account", "signOut": "Sign Out", "settings": "Settings", "notifications": "Notifications", "notSpecified": "Not specified", "noAddressProvided": "No address provided", "noEmail": "No email", "noEmailsListed": "No email addresses listed", "noNumber": "No number", "noPhoneNumbers": "No phone numbers listed", "noProfileImage": "No profile image", "noSpecializationsListed": "No specializations listed", "noCertificationsListed": "No certifications listed", "noEducationHistory": "No education history listed", "noJobTitle": "No job title", "notAssigned": "Not assigned", "noneAssigned": "None assigned", "notLinked": "Not linked", "noPhone": "No phone number", "unknown": "Unknown", "unknownInstitution": "Unknown Institution", "unknownDegree": "Unknown degree", "unknownField": "Unknown field", "primary": "Primary", "systemInformation": "System Information", "created": "Created", "lastUpdated": "Last Updated", "notAvailable": "N/A", "unnamed": "Unnamed", "issuer": "Issuer", "obtained": "Obtained", "expires": "Expires", "present": "Present", "notFound": {"title": "Page Not Found", "code": "404", "message": "The page you are looking for doesn't exist or has been moved."}, "placeholders": {"enterFirstName": "Enter first name", "enterLastName": "Enter last name", "enterFullAddress": "Enter full address", "enterProfileImageUrl": "Enter profile image URL", "enterEmailAddresses": "Enter email addresses (JSON format)", "enterPhoneNumbers": "Enter phone numbers (JSON format)", "enterEmployeeId": "Enter employee ID", "selectStatus": "Select status", "enterJobTitle": "Enter job title", "selectDepartment": "Select department", "selectGender": "Select gender", "enterSupervisorId": "Enter supervisor ID", "enterUserAccountId": "Enter user account ID (optional)", "enterSpecializations": "Enter specializations (comma separated)", "enterCertifications": "Enter certifications (JSON format)", "enterEducation": "Enter education history (JSON format)"}, "gender": {"notSpecified": "Not specified", "male": "Male", "female": "Female", "other": "Other", "preferNotToSay": "Prefer not to say"}, "department": {"notSpecified": "Not specified", "hr": "Human Resources", "it": "Information Technology", "finance": "Finance", "operations": "Operations", "sales": "Sales", "marketing": "Marketing"}, "edit": "Edit", "pagination": {"previous": "Previous", "next": "Next", "showing": "Showing {count} of {total} items"}, "INSERT": "Created", "UPDATE": "Updated", "DELETE": "Deleted"}, "errors": {"title": "Error", "networkError": "Network error. Please check your connection and try again.", "authError": "Authentication error. You may need to log in again.", "unexpectedError": "An unexpected error occurred. Please try again later.", "validationError": "Please check the form for errors.", "invalidEmail": "Invalid email format.", "invalidPhone": "Invalid phone number format.", "invalidLanguage": "Invalid language selection.", "requiredField": "This field is required.", "minLength": "This field must be at least {min} characters.", "maxLength": "This field must be at most {max} characters.", "invalidFormat": "Invalid format.", "duplicateEmail": "This email is already in use by another account."}, "home": {"title": "Si Simple 2025", "description": "A modern application with dark mode support", "demoText": "This is a demonstration of the design system with Tailwind CSS v4 and shadcn/ui components.", "themeToggleText": "Try toggling between light and dark mode using the button in the top-right corner.", "getStarted": "Get Started"}, "navigation": {"dashboard": "Dashboard", "user": "Profile", "contacts": "Contacts", "requests": "Requests", "caseFiles": "<PERSON><PERSON><PERSON>", "cases": "Cases", "scheduling": "Scheduling", "notes": "Notes", "reports": "Reports", "settings": "Settings", "admin": "Administration", "organizationAdmin": "System Adminiistration", "userManage": "User Access", "organizationProfile": "My Organization", "employeeManage": "Employee Management", "employeeAvailability": "Employee Availability", "documentManagement": "Document Management", "appointments": "Appointments", "assignments": "Assignments"}, "auth": {"signin": {"pageTitle": "Sign In", "pageDescription": "Sign in to your account", "title": "Sign in to your account", "subtitle": "Enter your credentials to access your account", "emailLabel": "Email address", "passwordLabel": "Password", "forgotPassword": "Forgot password?", "signInButton": "Sign In", "signingIn": "Signing In...", "noAccount": "Don't have an account?", "contactAdmin": "Contact your administrator", "errorMessage": "An error occurred during sign in", "unexpectedError": "An unexpected error occurred. Please try again."}, "signout": {"title": "Sign Out", "confirmMessage": "Are you sure you want to sign out?", "confirmButton": "Yes, Sign Out", "cancelButton": "Cancel"}, "resetPassword": {"pageTitle": "Reset Password", "pageDescription": "Reset your account password", "requestResetTitle": "Reset your password", "setNewPasswordTitle": "Set new password", "emailLabel": "Email address", "passwordLabel": "New password", "confirmPasswordLabel": "Confirm new password", "requestButton": "Send reset link", "resetButton": "Reset password", "backToSignIn": "Back to sign in", "requestSent": "Password reset link sent to your email", "passwordMismatch": "Passwords do not match", "resetSuccess": "Password reset successfully"}, "errors": {"notFound": "The authentication page you are looking for does not exist."}}, "user": {"profile": "Profile", "profileDescription": "Manage your profile information and preferences.", "personal": "Personal", "personalDescription": "Update your personal information.", "account": "Account", "accountDescription": "Manage your account information and password.", "contact": "Contact", "contactDescription": "Update your contact information.", "settings": "Settings", "settingsDescription": "Manage your account settings and preferences.", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "language": "Language", "notifications": "Notifications", "emailNotifications": "Email Notifications", "smsNotifications": "SMS Notifications", "inAppNotifications": "In-App Notifications", "profileUpdated": "Profile Updated", "personalInfoUpdated": "Personal information updated successfully.", "contactInfoUpdated": "Contact information updated successfully.", "settingsUpdated": "Settings updated successfully.", "name": "Name", "role": "Role", "selectRole": "Select role", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "systemInfo": "System Information", "createdAt": "Created At", "updatedAt": "Last Updated", "roles": {"director": "Director", "coordinator": "Coordinator", "socialWorker": "Social Worker", "systemAdmin": "System Admin"}, "management": {"title": "User Management", "description": "Manage users in your organization", "userList": "User List", "userListDescription": "Manage users in your organization", "createUser": "Create User", "createUserDescription": "Add a new user to your organization", "editUser": "Edit User", "editUserDescription": "Update user information", "userDetails": "User Details", "userCreated": "User created successfully", "userUpdated": "User updated successfully", "emailCannotBeChanged": "Email cannot be changed", "noUsersFound": "No users found", "filterByRole": "Filter by role", "userNotFound": "User Not Found", "userNotFoundDescription": "The user you are looking for does not exist or you don't have permission to view it."}}, "organization": {"list": "Organizations", "createNew": "Create New Organization", "noOrganizations": "No organizations found. Create your first organization to get started.", "details": "Organization Details", "backToList": "Back to List", "edit": "Edit", "createdAt": "Created on", "updatedAt": "Updated on", "assignDirector": "Assign Director", "profileManagement": "My Organization", "profileManagementDescription": "Manage your organization's profile, locations, services, and users", "generalInfo": "General Information", "generalInfoDescription": "Basic information about your organization", "locations": "Locations", "locationsDescription": "Manage your organization's locations and rooms", "services": "Services", "servicesDescription": "Configure the services your organization provides", "users": "Users", "usersDescription": "Manage users in your organization", "profileUpdated": "Organization profile updated successfully", "addLocation": "Add Location", "editLocation": "Edit Location", "deleteLocation": "Delete Location", "noLocations": "No locations found. Add your first location.", "locationCreated": "Location created successfully", "locationUpdated": "Location updated successfully", "deleteLocationConfirm": "Are you sure you want to delete this location?", "addService": "Add Service", "editService": "Edit Service", "deleteService": "Delete Service", "noServices": "No services found. Add your first service.", "serviceCreated": "Service created successfully", "serviceUpdated": "Service updated successfully", "deleteServiceConfirm": "Are you sure you want to delete this service?", "businessHours": "Business Hours", "businessHoursDescription": "Configure your organization's operating hours", "addBusinessHours": "Add Business Hours", "editBusinessHours": "Edit Business Hours", "deleteBusinessHours": "Delete Business Hours", "noBusinessHours": "No business hours found. Add your first business hours.", "businessHoursCreated": "Business hours created successfully", "businessHoursUpdated": "Business hours updated successfully", "deleteBusinessHoursConfirm": "Are you sure you want to delete these business hours?", "dayOfWeek": "Day of Week", "startTime": "Start Time", "endTime": "End Time", "isClosed": "Closed", "location": "Location", "allLocations": "All Locations", "description": "Description", "price": "Price", "duration": "Duration", "minutes": "minutes", "status": "Status", "active": "Active", "inactive": "Inactive", "cancel": "Cancel", "delete": "Delete", "rooms": "Rooms", "roomsDescription": "Manage rooms for your organization's locations", "addRoom": "Add Room", "editRoom": "Edit Room", "deleteRoom": "Delete Room", "noRooms": "No rooms found. Add your first room.", "roomCreated": "Room created successfully", "roomUpdated": "Room updated successfully", "deleteRoomConfirm": "Are you sure you want to delete this room?", "addRoomDescription": "Add a new room to one of your locations", "editRoomDescription": "Edit room details and features", "capacity": "Capacity", "features": "Features", "addCustomFeature": "Add Custom Feature", "featureName": "Feature Name", "featureDescription": "Feature Description", "add": "Add", "filterByLocation": "Filter by Location", "form": {"createOrganization": "Create Organization", "editOrganization": "Edit Organization", "organizationDetails": "Organization Details", "contactInfo": "Contact Information", "name": "Organization Name", "address": "Address", "phone": "Phone Number", "fax": "Fax Number", "email": "Email Address", "supportEmail": "Support Email", "website": "Website", "status": "Status", "active": "Active", "inactive": "Inactive", "suspended": "Suspended", "create": "Create Organization", "update": "Update Organization", "cancel": "Cancel", "required": "Required", "invalidEmail": "Invalid email address", "invalidPhone": "Invalid phone number", "firstName": "First Name", "lastName": "Last Name", "assignDirector": "Assign Director", "assignDirectorDescription": "Assign a director to this organization", "directorAssigned": "Director assigned successfully!", "assigning": "Assigning..."}, "director": {"assignDirector": "Assign Director", "directorDetails": "Enter the details for the organization director", "firstName": "First Name", "lastName": "Last Name", "email": "Email Address", "phone": "Phone Number", "assign": "Assign Director", "cancel": "Cancel"}}, "documents": {"title": "Document Attachments", "description": "Manage and organize document attachments across all entities", "uploadDocuments": "Upload Documents", "searchPlaceholder": "Search documents...", "allEntityTypes": "All Entity Types", "allDocumentTypes": "All Document Types", "entityTypes": {"caseFile": "<PERSON><PERSON><PERSON>", "contact": "Contacts", "appointment": "Appointments", "request": "Requests"}, "documentTypes": {"wordDocument": "Word Document", "imageJpg": "Image (JPG)", "imagePng": "Image (PNG)", "textFile": "Text File"}, "sortBy": {"uploadDate": "Sort by Upload Date", "name": "Sort by Name", "size": "Sort by <PERSON><PERSON>", "type": "Sort by Type"}, "filters": "Filters", "noDocumentsFound": "No documents found", "documentsFound": "{count} documents found", "errorLoadingAttachments": "Error Loading Attachments", "checkConfiguration": "Please check the configuration and try again.", "upload": {"title": "Upload Documents", "description": "Description", "selectFiles": "Select Files", "dragAndDrop": "Drag and drop files here, or click to select", "category": "Category", "tags": "Tags", "attachedTo": "Attached To", "entityType": "Entity Type", "entityId": "Entity ID", "uploadSuccess": "Upload successful", "uploadError": "Upload failed"}, "detail": {"title": "Document Details", "description": "Description", "back": "Back", "documentPreview": "Document Preview", "previewOf": "Preview of {name}", "previewNotAvailable": "Preview Not Available", "previewNotImplemented": "Preview for {type} files is not yet implemented", "download": "Download", "openInNewTab": "Open in New Tab", "documentInformation": "Document Information", "type": "Type", "size": "Size", "status": "Status", "source": "Source", "manualUpload": "Manual Upload", "generated": "Generated", "attachedTo": "Attached To", "entityType": "Entity Type", "entityId": "Entity ID", "viewEntity": "View Entity", "viewRelated": "Open Related ➔", "metadata": "<PERSON><PERSON><PERSON>", "category": "Category", "tags": "Tags", "uploadInformation": "Upload Information", "uploaded": "Uploaded", "uploadedBy": "Uploaded By", "actions": "Actions", "editMetadata": "<PERSON>", "delete": "Delete", "deleting": "Deleting...", "deleteConfirmation": "Are you sure you want to delete this document? This action cannot be undone.", "timeline": "Timeline", "language": "Language", "documentInfo": "Details", "fileName": "<PERSON><PERSON>", "fileSize": "Size"}}, "appointments": {"title": "Appointments", "description": "Manage and view all appointments", "newAppointment": "New Appointment", "createAppointment": "Create New Appointment", "editAppointment": "Edit Appointment", "appointmentDetails": "Appointment Details", "appointmentInformation": "Appointment Information", "scheduleOverview": "Schedule Overview", "calendarView": "Calendar View", "listView": "List View", "backToAppointments": "Back to Appointments", "backToList": "Back to List", "backToDetails": "Back to Details", "viewFullDetails": "View Full Details", "close": "Close", "apply": "Apply", "filterAppointments": "Filter Appointments", "searchAppointments": "Search appointments...", "allStatuses": "All statuses", "activeFilters": "Active filters:", "noAppointmentsFound": "No appointments found", "loadingAppointments": "Loading appointments...", "loadingCalendar": "Loading calendar...", "loadingForm": "Loading form...", "loadingAppointmentDetails": "Loading appointment details...", "errorCreatingAppointment": "Error Creating Appointment", "appointmentNotFound": "Appointment Not Found", "appointmentNotFoundDescription": "The appointment you're looking for doesn't exist.", "appointmentEditNotFoundDescription": "The appointment you're trying to edit doesn't exist or you don't have permission to edit it.", "errorLoadingForm": "Something went wrong while loading the appointment form.", "scheduleAppointment": "Schedule a new appointment", "updateAppointmentInformation": "Update appointment information", "selfContained": "Self-Contained", "noCaseFileLink": "No Case File Link", "noServiceLink": "No Service Link", "integrationStatus": "Integration Status", "integrationDescription": "This appointment is operating in self-contained mode and can be integrated with other systems later.", "basicInformation": "Basic Information", "schedule": "Schedule", "timeline": "Timeline", "systemInformation": "System Information", "appointmentId": "Appointment ID", "organizationId": "Organization ID", "form": {"title": "Title", "titlePlaceholder": "Enter appointment title", "description": "Description", "descriptionPlaceholder": "Enter appointment description (optional)", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "status": "Status", "selectStatus": "Select status", "createAppointment": "Create Appointment", "updateAppointment": "Update Appointment", "cancel": "Cancel", "creating": "Creating...", "updating": "Updating...", "validation": {"titleRequired": "Title is required", "titleTooShort": "Title is too short", "titleTooLong": "Title is too long", "descriptionTooLong": "Description is too long", "dateRequired": "Date is required", "startTimeRequired": "Start time is required", "endTimeRequired": "End time is required", "endTimeAfterStart": "End time must be after start time", "pastDate": "Cannot create appointments in the past"}}, "status": {"planned": "Planned", "confirmed": "Confirmed", "inProgress": "In Progress", "completed": "Completed", "missed": "Missed", "postponed": "Postponed", "cancelled": "Cancelled"}, "statusDescriptions": {"planned": "Appointment is scheduled but not confirmed", "confirmed": "Appointment is confirmed by all parties", "inProgress": "Appointment is currently happening", "completed": "Appointment has been completed successfully", "missed": "Appointment was missed by attendee", "postponed": "Appointment has been postponed to a later date", "cancelled": "Appointment has been cancelled"}, "actions": {"view": "View appointment", "edit": "Edit appointment", "confirm": "Confirm", "complete": "Complete", "cancel": "Cancel", "postpone": "Postpone", "reschedule": "Reschedule"}, "messages": {"created": "Appointment created successfully", "updated": "Appointment updated successfully", "deleted": "Appointment deleted successfully", "confirmed": "Appointment confirmed successfully", "completed": "Appointment completed successfully", "cancelled": "Appointment cancelled successfully", "postponed": "Appointment postponed successfully", "conflictDetected": "Time conflict detected", "conflictMessage": "There is already an appointment scheduled during this time"}}, "caseFileOpening": {"wizard": {"welcome": {"title": "Welcome to Case File Opening", "subtitle": "Let's get your case file ready for active management", "description": "This guided process will help you set up document workflows, contact management, and signature requirements for this case file.", "features": {"documents": {"title": "Document Management", "description": "Organize and track all case-related documents with digital signatures"}, "contacts": {"title": "Contact Workflow", "description": "Manage family contacts and their document requirements"}, "tracking": {"title": "Progress Tracking", "description": "Monitor completion status and required actions in real-time"}}, "getStarted": "Get Started"}, "contacts": {"title": "Family Contacts", "subtitle": "Review the contacts associated with this case file", "description": "These contacts will have access to case documents and may need to complete signature requirements.", "noContacts": "No contacts found", "noContactsDescription": "Add contacts to get started with document workflows", "continueToDocuments": "Continue to Documents"}, "documents": {"title": "Document Workflow Ready", "subtitle": "Your case file is now set up for document management", "successMessage": "Perfect! You're all set.", "successDescription": "Select a contact and complete the required document actions."}, "breadcrumb": {"welcome": "Welcome", "contacts": "Contacts", "documents": "Documents", "complete": "Complete"}}, "contacts": {"title": "Contacts", "description": "Click on a contact to view their documents", "noContacts": "No contacts found", "noContactsDescription": "Add contacts to get started"}, "documents": {"title": "Documents for {contactName}", "noDocuments": "No Documents Found", "noDocumentsDescription": "No documents have been assigned to this contact yet.", "progressSummary": "Progress Summary", "progressDescription": "{completed} of {total} signature-required documents completed", "complete": "Complete", "status": {"completed": "Completed", "needsSignature": "Needs Signature", "needsReview": "Needs Review", "rejected": "Rejected", "expired": "Expired", "review": "Review"}, "actions": {"viewDocument": "View Document", "signDocument": "Sign Document"}, "badges": {"signatureRequired": "Signature Required", "required": "Required"}, "signedOn": "Signed on {date}"}, "navigation": {"backToContacts": "Back to Contacts", "viewingDocumentsFor": "Viewing documents for"}, "caseFile": {"opening": "Opening", "service": "Service", "reference": "Reference", "assignedTo": "Assigned to", "requestDate": "Request Date"}, "emptyDocumentState": {"title": "Document Review & Signature Workflow", "description": "This workflow is for document reviews, signatures, and acknowledgments.", "instruction": "Please click on a contact from the list to view their documents and take required actions.", "features": {"viewDocuments": "View Documents", "digitalSignatures": "Digital Signatures", "acknowledgments": "Acknowledgments", "progressTracking": "Progress Tracking"}}}, "template-single-crud": {"feature-1": {"title": "Feature 1", "description": "Feature 1 description", "createTitle": "Create New Item", "createDescription": "Enter the details for the new item", "editTitle": "<PERSON>em", "editDescription": "Update the details for this item", "viewTitle": "<PERSON><PERSON>", "removeTitle": "Remove Item", "removeDescription": "Are you sure you want to remove this item?", "removeConfirm": "Yes, Remove", "removeCancel": "No, Cancel", "backToList": "Back to List", "itemCreated": "Item created successfully", "itemUpdated": "Item updated successfully", "itemRemoved": "<PERSON><PERSON> removed successfully", "noItems": "No items found. Create your first item to get started.", "fields": {"name": "Name", "description": "Description", "status": "Status", "createdAt": "Created At", "updatedAt": "Updated At"}, "status": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "actions": {"create": "Create <PERSON><PERSON>", "edit": "Edit", "view": "View", "remove": "Remove"}}}, "automation": {"dashboard": {"title": "Automation Dashboard", "employeeCreation": {"title": "Employee Creation", "description": "Create a new employee record", "content": "Use this wizard to create a new employee record with all necessary information.", "startButton": "Start Wizard"}, "requestCreation": {"title": "Request Creation", "description": "Create a new service request", "content": "Use this wizard to create a new service request with all necessary details.", "startButton": "Start Wizard"}}, "execution": {"title": "Workflow Execution Status", "description": "Track the status of your workflow execution"}, "request-wizard": {"title": "Create Request", "description": "Complete the following steps to create a new request", "startButton": "Start Request Creation", "steps": {"basic_info": "Basic Info", "related_contacts": "Related Contacts", "service_requirements": "Service Requirements", "family_availability": "Family Availability", "review": "Review"}, "basic_info": {"service": "Service", "selectService": "Select a service", "location": "Location", "selectLocation": "Select a location"}, "service_requirements": {"startDate": "Start Date", "selectStartDate": "Select start date", "endDate": "End Date", "selectEndDate": "Select end date", "frequency": "Frequency", "selectFrequency": "Select frequency", "periodicity": "Periodicity", "selectPeriodicity": "Select periodicity", "frequencyCount": "Frequency", "enterFrequencyCount": "Enter number of times per period", "timesPerPeriod": "times per period", "daily": "Daily", "weekly": "Weekly", "biweekly": "Bi-weekly", "monthly": "Monthly", "custom": "Custom", "duration": "Duration (minutes)", "enterDuration": "Enter duration in minutes", "preferredDays": "Preferred Days", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday", "preferredTime": "Preferred Time of Day", "selectPreferredTime": "Select preferred time", "morning": "Morning (8am-12pm)", "afternoon": "Afternoon (12pm-5pm)", "evening": "Evening (5pm-9pm)", "flexible": "Flexible", "notes": "Additional Notes", "enterNotes": "Enter any additional requirements or notes"}, "related_contacts": {"title": "Add Related Contacts", "description": "Select contacts that are related to this request. You can add multiple contacts with different relationships.", "addContact": "Add Contact", "noContacts": "No contacts added yet. Click the button above to add contacts.", "selectedContacts": "Selected Contacts", "addNewContact": "Add New Contact", "contact": "Contact", "searchContact": "Search for a contact...", "noContactsFound": "No contacts found", "minCharactersMessage": "Type at least 2 characters to search", "relationship": "Relationship Type", "selectRelationship": "Select relationship type", "client": "Client", "familyMember": "Family Member", "guardian": "Legal Guardian", "caregiver": "Caregiver", "professional": "Professional", "other": "Other", "actions": "Actions", "remove": "Remove"}, "review": {"title": "Review", "description": "Please review the information below before submitting your request.", "basicInfo": "Basic Information", "complete": "Complete", "incomplete": "Incomplete", "serviceRequirements": "Service Requirements", "familyAvailability": "Family Availability", "relatedContacts": "Related Contacts", "submit": "Submit Request", "notProvided": "Not provided", "notSpecified": "Not specified", "noRelatedContacts": "No related contacts added"}, "family_availability": {"title": "Family Availability", "generalAvailability": "General Availability", "weekdaysOnly": "Weekdays only", "weekendsOnly": "Weekends only", "bothWeekdaysWeekends": "Both weekdays and weekends", "flexible": "Flexible", "timeFlexibility": "Time Flexibility", "selectTimeFlexibility": "Select time flexibility", "veryFlexible": "Very flexible (can accommodate any time)", "somewhatFlexible": "Somewhat flexible (prefer certain times but can adjust)", "limitedFlexibility": "Limited flexibility (need specific times)", "advanceNotice": "Advance Notice Required", "selectAdvanceNotice": "Select advance notice required", "hours24": "24 hours", "days2": "2-3 days", "week1": "1 week", "weeks2": "2 weeks", "month1": "1 month or more", "notes": "Additional Notes", "enterNotes": "Enter any additional notes about availability"}}}, "request": {"title": "Requests", "description": "Manage your requests", "createNew": "Create New Request", "noRequests": "No requests found. Create your first request to get started.", "details": "Request Details", "viewTitle": "View Request", "viewDescription": "View and manage request information", "backToList": "Back to List", "actions": "Actions", "historyTitle": "Request History", "historyDescription": "Timeline of changes to this request", "advancedFilters": "Advanced Filters", "searchRequests": "Search requests...", "status": {"draft": "Draft", "processing": "Processing", "waitlist": "Waitlist", "completed": "Completed", "closed": "Closed"}, "statusDescriptions": {"draft": "Initial state, request is being prepared and not yet submitted", "processing": "Request has been submitted and is being processed according to business rules", "waitlist": "Service cannot be provided immediately due to resource constraints", "completed": "Service can be provided, case file has been initiated", "closed": "Request has been manually closed and is no longer active"}, "priority": {"low": "Low", "medium": "Medium", "high": "High"}, "serviceType": {"supervised_visitation": "Supervised Visitation", "exchange": "Exchange", "transportation": "Transportation", "consultation": "Consultation", "other": "Other"}, "fields": {"title": "Title", "description": "Description", "status": "Status", "priority": "Priority", "serviceType": "Service Type", "createdAt": "Created At", "updatedAt": "Updated At", "requester": "Requester", "assignee": "Assignee", "startDate": "Start Date", "endDate": "End Date", "location": "Location", "service": "Service", "frequency": "Frequency", "duration": "Duration", "preferredDays": "Preferred Days", "preferredTime": "Preferred Time", "referenceNumber": "Reference Number", "contacts": "Contacts", "relatedContacts": "Related Contacts", "actions": "Actions"}, "notSpecified": "Not specified", "minutes": "minutes", "serviceTimeline": "Service Timeline", "requestCreated": "Request Created", "currentStatus": "Current Status", "serviceStart": "Service Start", "serviceEnd": "Service End", "noContactsAvailable": "No contacts available", "noLocationSpecified": "No location specified", "uniqueIdentifier": "Unique identifier", "weekdaysOnly": "Weekdays only", "weekendsOnly": "Weekends only", "weekdaysAndWeekends": "Weekdays and weekends", "flexible": "Flexible", "periodicity": "Periodicity", "frequencyCount": "Frequency", "timesPerPeriod": "times per period", "periodicities": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}, "wizard": {"wizardTitle": "Create New Request", "wizardDescription": "Complete the following steps to create a new request", "steps": {"basicInfo": "Basic Info", "serviceRequirements": "Service Requirements", "familyAvailability": "Family Availability", "review": "Review"}, "stepTitles": {"basicInfo": "Basic Information", "serviceRequirements": "Service Requirements", "familyAvailability": "Family Availability", "review": "Review and Submit"}, "buttons": {"nextServiceRequirements": "Next: Service Requirements", "nextFamilyAvailability": "Next: Family Availability", "nextReview": "Next: Review", "submit": "Submit Request", "previous": "Previous", "startOver": "Start Over"}, "success": {"title": "Request Created Successfully", "message": "Your request has been created and is now in draft status.", "viewRequest": "View Request", "createAnother": "Create Another Request"}}, "serviceRequirements": {"title": "Service Requirements", "description": "Specify the requirements for this service request", "frequency": "Frequency", "duration": "Duration (minutes)", "specialRequirements": "Special Requirements", "preferredDays": "Preferred Days", "preferredTime": "Preferred Time of Day", "notes": "Additional Notes", "frequencies": {"weekly": "Weekly", "biweekly": "Bi-weekly", "monthly": "Monthly", "oneTime": "One-time"}, "timeOfDay": {"morning": "Morning", "afternoon": "Afternoon", "evening": "Evening", "noPreference": "No Preference"}, "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "familyAvailability": {"title": "Family Availability", "description": "Provide information about family availability", "generalAvailability": "General Availability", "specificDates": "Specific Dates", "notes": "Additional Notes", "addDate": "Add Date", "removeDate": "Remove", "date": "Date", "startTime": "Start Time", "endTime": "End Time", "selectContact": "Select a contact to set their availability", "changesAutoSaved": "Changes auto-saved", "clickToMark": "Click on time slots to mark as available", "timeSlots": "time slots", "noContactsAdded": "No contacts added yet.", "addContacts": "Add Contacts", "selectToSet": "Select a contact to set their availability", "weeklyAvailability": "Weekly Availability", "morning": "Morning", "noon": "<PERSON>on", "afternoon": "Afternoon", "evening": "Evening", "available": "Available", "noAvailabilitySet": "No availability set", "availabilitySetFor": "Availability set for", "contacts": "contacts", "seeDetails": "See details in the availability grid", "noAvailabilityInfo": "No availability information provided"}}, "employee": {"management": {"title": "Employee Management", "description": "Manage employee profiles and availability", "createTitle": "Create New Employee", "createDescription": "Enter the details for the new employee", "editTitle": "Edit Employee", "editDescription": "Update the details for this employee", "viewTitle": "Employee Details", "removeTitle": "Remove Employee", "removeDescription": "Are you sure you want to remove this employee?", "removeConfirm": "Yes, Remove", "removeCancel": "No, Cancel", "removeWarning": "This action cannot be undone. The employee will be marked as terminated.", "backToList": "Back to List", "itemCreated": "Employee created successfully", "itemUpdated": "Employee updated successfully", "itemRemoved": "Employee removed successfully", "noItems": "No employees found. Create your first employee to get started.", "personalInformation": "Personal Information", "tabs": {"contact": "Contact Information", "employment": "Employment Details", "professional": "Professional Information", "personal": "Personal Information"}, "fields": {"name": "Name", "description": "Description", "status": "Status", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "profileImage": "Profile Image", "dateOfBirth": "Date of Birth", "gender": "Gender", "address": "Address", "employeeId": "Employee ID", "hireDate": "Hire Date", "terminationDate": "Termination Date", "employmentStatus": "Employment Status", "jobTitle": "Job Title", "department": "Department", "supervisor": "Supervisor", "specializations": "Specializations", "certifications": "Certifications", "education": "Education", "emails": "Email Addresses", "phones": "Phone Numbers", "userAccount": "User Account", "createdAt": "Created At", "updatedAt": "Updated At"}, "status": {"active": "Active", "inactive": "Inactive", "draft": "Draft"}, "employmentStatus": {"active": "Active", "inactive": "Inactive", "terminated": "Terminated"}, "actions": {"create": "Create Employee", "edit": "Edit", "view": "View", "remove": "Remove", "activate": "Activate", "deactivate": "Deactivate", "linkUserAccount": "Link User Account"}, "confirmDeactivate": "Confirm Deactivation", "deactivateDescription": "Are you sure you want to deactivate this employee? They will no longer have access to the system.", "activating": "Activating...", "deactivating": "Deactivating..."}, "wizard": {"title": "Employee Creation Wizard", "description": "Create a new employee using this step-by-step wizard", "createTitle": "Create a New Employee", "createDescription": "This wizard will guide you through the process of creating a new employee record. You can save your progress at any time and come back later to complete it.", "startButton": "Start Employee Creation", "steps": {"basicInfo": "Basic Info", "contactInfo": "Contact Info", "employmentDetails": "Employment Details", "review": "Review"}, "stepTitles": {"basicInfo": "Basic Information", "contactInfo": "Contact Information", "employmentDetails": "Employment Details", "review": "Review Employee Information"}, "wizardTitle": "Create New Employee", "wizardDescription": "Complete the following steps to create a new employee", "fields": {"firstName": "First Name", "lastName": "Last Name", "jobTitle": "Job Title", "department": "Department", "address": "Address", "email": "Email", "phone": "Phone", "employmentStatus": "Employment Status", "hireDate": "Hire Date", "employeeId": "Employee ID", "supervisorId": "Supervisor ID", "role": "User Role"}, "placeholders": {"enterFirstName": "Enter first name", "enterLastName": "Enter last name", "enterJobTitle": "Enter job title", "selectDepartment": "Select department", "enterAddress": "Enter address", "enterEmail": "Enter email address", "enterPhone": "Enter phone number", "selectEmploymentStatus": "Select employment status", "enterHireDate": "Enter hire date", "enterEmployeeId": "Enter employee ID", "enterSupervisorId": "Enter supervisor ID", "selectRole": "Select role"}, "buttons": {"previous": "Previous", "next": "Next", "save": "Save", "submit": "Submit", "previousBasicInfo": "Previous: Basic Information", "previousContactInfo": "Previous: Contact Information", "previousEmploymentDetails": "Previous: Employment Details", "nextContactInfo": "Next: Contact Information", "nextEmploymentDetails": "Next: Employment Details", "nextReview": "Next: Review", "createEmployee": "Create Employee"}, "reviewDescription": "Please review the information below before submitting. You can go back to any step to make changes.", "reviewTable": {"field": "Field", "value": "Value"}, "success": {"title": "Employee Created Successfully!", "description": "The employee has been created successfully. You can now view the employee details or create another employee.", "executionId": "Execution ID:", "status": "Status:", "createdAt": "Created At:", "completed": "Completed", "viewEmployee": "View Employee", "createAnother": "Create Another Employee"}, "loading": "Loading...", "roles": {"director": "Director", "coordinator": "Coordinator", "socialWorker": "Social Worker"}}, "availability": {"title": "Availability Management", "description": "Manage employee availability, time-off requests, and exceptions", "listTitle": "Employee Availability", "listDescription": "View and manage employee availability", "noItems": "No availability records found", "createButton": "Add Availability", "viewTitle": "Availability Details", "viewDescription": "View employee availability details", "editButton": "Edit Availability", "backToList": "Back to List", "editTitle": "Edit Availability", "editDescription": "Update employee availability", "saveButton": "Save Changes", "cancelButton": "Cancel", "itemUpdated": "Availability updated successfully", "createTitle": "Add Availability", "createDescription": "Create new employee availability", "itemCreated": "Availability created successfully", "fields": {"employee": "Employee", "dayOfWeek": "Day of Week", "startTime": "Start Time", "endTime": "End Time", "isRecurring": "Recurring", "recurrencePattern": "Recurrence <PERSON>", "startDate": "Start Date", "endDate": "End Date", "type": "Type", "status": "Status", "description": "Description", "exceptionDate": "Exception Date", "isAvailable": "Available", "reason": "Reason"}, "daysOfWeek": {"sunday": "Sunday", "monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday"}, "timeOffTypes": {"vacation": "Vacation", "sick_leave": "Sick Leave", "personal_leave": "Personal Leave", "other": "Other"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "cancelled": "Cancelled"}, "tabs": {"regularHours": "Regular Hours", "timeOff": "Time Off", "exceptions": "Exceptions"}, "actions": {"approve": "Approve", "reject": "Reject", "cancel": "Cancel"}, "featureErrors": {"invalidTimeRange": "Invalid time range. End time must be after start time.", "invalidDateRange": "Invalid date range. End date must be after start date.", "overlappingAvailability": "This availability overlaps with an existing one.", "overlappingTimeOff": "This time-off request overlaps with an existing one."}, "regularHoursTitle": "Weekly Availability", "regularHoursDescription": "Set your regular weekly availability", "timeOff": {"title": "Time Off", "createTitle": "Request Time Off", "editTitle": "Edit Time Off Request", "createDescription": "Submit a new time off request", "editDescription": "Edit your time off request", "listTitle": "Time Off Requests", "noRecords": "No time off requests found", "addButton": "Request Time Off", "startDate": "Start Date", "endDate": "End Date", "startTime": "Start Time (optional)", "endTime": "End Time (optional)", "type": "Type", "selectType": "Select type", "description": "Reason/Notes", "createSuccess": "Time off request created successfully", "updateSuccess": "Time off request updated successfully", "deleteSuccess": "Time off request deleted successfully", "approveSuccess": "Time off request approved", "rejectSuccess": "Time off request rejected", "cancelSuccess": "Time off request cancelled", "deleteConfirmTitle": "Delete Time Off Request", "deleteConfirmMessage": "Are you sure you want to delete this time off request? This action cannot be undone.", "approveButton": "Approve", "rejectButton": "Reject", "cancelButton": "Cancel Request", "types": {"vacation": "Vacation", "sickLeave": "Sick Leave", "personalLeave": "Personal Leave", "other": "Other"}, "status": {"pending": "Pending", "approved": "Approved", "rejected": "Rejected", "cancelled": "Cancelled"}}, "exceptions": {"title": "Availability Exceptions", "createTitle": "Create Exception", "editTitle": "Edit Exception", "createDescription": "Create a one-time exception to regular availability", "editDescription": "Edit availability exception", "listTitle": "Availability Exceptions", "noRecords": "No exceptions found", "addButton": "Add Exception", "date": "Date", "reason": "Reason", "reasonPlaceholder": "Explain why this exception is needed", "availableLabel": "Available", "unavailableLabel": "Unavailable", "createSuccess": "Exception created successfully", "updateSuccess": "Exception updated successfully", "deleteSuccess": "Exception deleted successfully", "deleteConfirmTitle": "Delete Exception", "deleteConfirmMessage": "Are you sure you want to delete this exception? This action cannot be undone."}}}, "contact": {"management": {"title": "Contacts", "description": "Manage your contacts", "createTitle": "Create Contact", "createDescription": "Add a new contact to your organization", "editTitle": "Edit Contact", "editDescription": "Update contact information", "removeTitle": "Remove Contact", "removeDescription": "Are you sure you want to remove this contact?", "removeConfirm": "Remove", "backToList": "Back to List", "itemUpdated": "Contact updated successfully", "noItems": "No contacts found", "fields": {"name": "Name", "type": "Type", "status": "Status", "email": "Email", "emailPersonal": "<PERSON><PERSON> (Personal)", "emailWork": "<PERSON><PERSON> (Work)", "phone": "Phone", "phoneMobile": "Phone (Mobile)", "phoneHome": "Phone (Home)", "address": "Address", "notes": "Notes", "searchPlaceholder": "Search contacts...", "placeholders": {"name": "Enter name", "emailPersonal": "Enter personal email", "emailWork": "Enter work email", "phoneMobile": "Enter mobile phone", "phoneHome": "Enter home phone", "address": "Enter address", "status": "Select status"}}, "status": {"active": "Active", "inactive": "Inactive", "archived": "Archived", "draft": "Draft"}, "type": {"family": "Family", "professional": "Professional", "organization": "Organization", "other": "Other"}, "actions": {"create": "Create", "edit": "Edit", "view": "View", "remove": "Remove", "search": "Search", "removing": "Removing...", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "creating": "Creating..."}, "messages": {"removeWarning": "This action cannot be undone. The contact will be marked as inactive."}, "history": {"title": "History", "description": "Recent changes to this contact", "contactHistoryTitle": "Contact History", "noChanges": "No changes recorded yet", "unknownUser": "Unknown User", "recordDeleted": "Record was deleted", "actions": {"insert": "Created", "update": "Updated", "delete": "Deleted"}}, "relationships": {"title": "Relationships", "description": "Manage relationships for this contact", "createTitle": "Create Relationship", "createDescription": "Add a new relationship for this contact", "editTitle": "Edit Relationship", "editDescription": "Update relationship information", "removeTitle": "Remove Relationship", "removeDescription": "Are you sure you want to remove this relationship?", "removeConfirm": "Remove", "backToContact": "Back to Contact", "noRelationships": "No relationships found", "relationshipCreated": "Relationship created successfully", "relationshipUpdated": "Relationship updated successfully", "relationshipRemoved": "Relationship removed successfully", "unknownContact": "Unknown Contact", "fields": {"relatedContact": "Related Contact", "relationship": "Relationship Type", "selectContact": "Select a contact", "selectRelationship": "Select relationship type", "searchContacts": "Search contacts...", "noContactsFound": "No contacts found", "minCharactersMessage": "Type at least 2 characters to search", "pleaseSelectContact": "Please select a contact"}, "categories": {"family": "Family", "professional": "Professional", "other": "Other"}, "types": {"parent": "Parent", "child": "Child", "sibling": "Sibling", "spouse": "Spouse", "grandparent": "Grandparent", "grandchild": "Grandchild", "aunt_uncle": "Aunt/Uncle", "niece_nephew": "Ni<PERSON>e/Nephew", "cousin": "Cousin", "step_parent": "Step-Parent", "step_child": "Step-Child", "guardian": "Guardian", "ward": "Ward", "lawyer": "Lawyer", "client": "Client", "social_worker": "Social Worker", "case_manager": "Case Manager", "doctor": "Doctor", "patient": "Patient", "therapist": "Therapist", "friend": "Friend", "neighbor": "Neighbor", "colleague": "Colleague", "other": "Other"}}}}, "caseFile": {"active": {"title": "Active Case File", "description": "Manage active case file information and documents", "dashboard": {"title": "Dashboard", "quickActions": {"title": "Quick Actions", "schedule": "Schedule", "addDocument": "Add Document", "createNote": "Create Note", "viewHistory": "View History"}, "familyInformation": {"title": "Family Information", "description": "Contact details and relationships", "children": "Children", "adults": "Adults", "professionals": "Professionals", "noContacts": "No contacts found", "noContactsDescription": "Add contacts to get started", "viewAllContacts": "View All Contacts"}, "serviceRequirements": {"title": "Service Requirements", "description": "Progress, appointments, and pending items", "viewDetails": "View Full Request", "noServiceRequest": "No service request found", "noServiceRequestDescription": "Service information will appear here once a request is linked"}, "appointmentSummary": {"title": "Appointments", "today": "Today", "tomorrow": "Tomorrow", "upcoming": "Upcoming", "overdue": "Overdue", "noAppointments": "No upcoming appointments", "viewAll": "View All Appointments", "status": {"planned": "Planned", "confirmed": "Confirmed", "completed": "Completed", "missed": "Missed", "cancelled": "Cancelled"}}}, "documents": {"title": "Documents", "caseDocuments": "Case Documents", "contactDocuments": "Contact Documents", "noDocuments": "No documents found", "noDocumentsDescription": "Upload documents to get started", "uploadDocument": "Upload Document", "viewDocument": "View Document", "downloadDocument": "Download Document", "deleteDocument": "Delete Document", "documentUploaded": "Document uploaded successfully", "documentDeleted": "Document deleted successfully"}, "contacts": {"title": "Case File Contacts", "addContact": "Add Contact", "removeContact": "Remove Contact", "viewContact": "View Contact", "noContacts": "No contacts found", "noContactsDescription": "Add contacts to get started", "contactAdded": "Contact added successfully", "contactRemoved": "Contact removed successfully", "removeContactConfirm": "Are you sure you want to remove {name} from this case file?", "addContactModal": {"title": "Add Contact to Case File", "description": "Search for a contact and specify their relationship to add them to this case file.", "searchPlaceholder": "Search for a contact...", "relationshipType": "Relationship Type", "selectRelationshipType": "Select relationship type", "addContact": "Add Contact", "adding": "Adding...", "cancel": "Cancel"}, "relationshipTypes": {"parent": "Parent", "child": "Child", "guardian": "Guardian", "sibling": "Sibling", "grandparent": "Grandparent", "other_family": "Other Family", "social_worker": "Social Worker", "therapist": "Therapist", "teacher": "Teacher", "doctor": "Doctor", "lawyer": "Lawyer", "other_professional": "Other Professional", "friend": "Friend", "neighbor": "Neighbor", "other": "Other"}, "contactInfo": {"email": "Email", "phone": "Phone", "address": "Address", "personalEmail": "Personal Email", "workEmail": "Work Email", "mobilePhone": "Mobile Phone", "homePhone": "Home Phone", "workPhone": "Work Phone", "noEmailsListed": "No email addresses listed", "noPhoneNumbers": "No phone numbers listed", "noAddressProvided": "No address provided"}, "contactDrawer": {"title": "Contact Details", "contactInformation": "Contact Information", "close": "Close"}}, "history": {"title": "Case File History", "description": "Timeline of changes to this case file", "noHistory": "No history found", "noHistoryDescription": "Changes will appear here as they are made", "changeDetails": "Change Details", "viewDetails": "View Details", "hideDetails": "Hide Details", "changedBy": "Changed by", "changedAt": "Changed at", "actions": {"INSERT": "Created", "UPDATE": "Updated", "DELETE": "Deleted"}}, "tabs": {"dashboard": "Dashboard", "serviceRequest": "Service Request", "documents": "Documents", "contacts": "Contacts", "appointments": "Appointments", "history": "History"}, "serviceRequest": {"title": "Service Request", "description": "Complete service request information and requirements", "serviceDetails": "Service Details", "requestDetails": "Request Details", "locationDetails": "Location Details", "contactDetails": "Contact Details", "scheduleDetails": "Schedule Details", "requirements": "Requirements", "noRequest": "No Service Request", "noRequestDescription": "No service request information is available for this case file.", "serviceName": "Service Name", "serviceDescription": "Service Description", "requestNumber": "Request Number", "requestTitle": "Request Title", "requestDescription": "Request Description", "startDate": "Start Date", "endDate": "End Date", "duration": "Duration", "frequency": "Frequency", "location": "Location", "address": "Address", "contactPerson": "Contact Person", "phone": "Phone", "email": "Email", "status": "Status", "priority": "Priority", "objectives": "Objectives", "specialRequirements": "Special Requirements", "frequencyCount": "Frequency Count"}, "appointments": {"title": "Case File Appointments", "description": "Appointments associated with this case file", "noAppointments": "No appointments found", "noAppointmentsDescription": "No appointments have been scheduled for this case file yet", "viewAppointment": "View Appointment", "scheduleNew": "Schedule New Appointment"}}}}