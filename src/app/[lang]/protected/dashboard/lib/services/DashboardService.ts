import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";

export interface TodayAppointment {
  id: string;
  title: string;
  time: string;
  duration: string;
  status: string;
  room?: string;
  location?: string;
  case_file_id?: string | null;
}

export interface UrgentTask {
  id: string;
  title: string;
  dueDate: string;
  priority: 'high' | 'medium' | 'low';
  type: 'report' | 'approval' | 'document' | 'appointment';
  entity_id?: string;
  entity_type?: string;
}

export interface RecentCaseFile {
  id: string;
  title: string;
  lastUpdate: string;
  status: string;
  case_number: string;
}

export interface NotificationItem {
  id: string;
  title: string;
  message: string;
  timestamp: string;
  type: 'success' | 'warning' | 'info' | 'error';
  read: boolean;
  entity_id?: string;
  entity_type?: string;
}

export class DashboardService {
  private static instance: DashboardService;

  private constructor() {}

  public static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  /**
   * Get today's appointments for the current user
   */
  async getTodayAppointments(): Promise<{
    success: boolean;
    data?: TodayAppointment[];
    error?: string;
  }> {
    try {
      // Get current user and organization
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      // Query appointments for today where the current user is assigned
      const { data, error } = await supabase
        .from("appointments")
        .select(`
          id,
          title,
          description,
          appointment_date,
          start_time,
          end_time,
          duration_minutes,
          status,
          case_file_id,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            id,
            employees!appointment_assignments_employee_id_fkey(
              id,
              user_id,
              first_name,
              last_name
            )
          ),
          appointment_rooms!appointment_rooms_appointment_id_fkey(
            id,
            rooms!appointment_rooms_room_id_fkey(
              id,
              name,
              locations!rooms_location_id_fkey(
                id,
                name
              )
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .eq("appointment_date", today)
        .order("start_time", { ascending: true });

      if (error) {
        return {
          success: false,
          error: `Failed to fetch appointments: ${error.message}`,
        };
      }

      // Filter appointments where current user is assigned
      const userAppointments = (data || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      });

      // Transform to simplified format for dashboard
      const todayAppointments: TodayAppointment[] = userAppointments.map((appointment) => {
        const room = appointment.appointment_rooms?.[0]?.rooms;
        const location = room?.locations?.name;

        return {
          id: appointment.id,
          title: appointment.title,
          time: appointment.start_time,
          duration: `${appointment.duration_minutes}min`,
          status: appointment.status,
          room: room?.name,
          location: location,
          case_file_id: appointment.case_file_id,
        };
      });

      return {
        success: true,
        data: todayAppointments,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get urgent tasks for the current user
   */
  async getUrgentTasks(): Promise<{
    success: boolean;
    data?: UrgentTask[];
    error?: string;
  }> {
    try {
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();
      const urgentTasks: UrgentTask[] = [];

      // Get overdue appointments (should have been completed but are still in progress)
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const yesterdayStr = yesterday.toISOString().split('T')[0];

      const { data: overdueAppointments } = await supabase
        .from("appointments")
        .select(`
          id,
          title,
          appointment_date,
          status,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            employees!appointment_assignments_employee_id_fkey(
              user_id
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .lt("appointment_date", yesterdayStr)
        .in("status", ["planned", "confirmed"])
        .order("appointment_date", { ascending: true });

      // Filter for user's appointments and add to urgent tasks
      const userOverdueAppointments = (overdueAppointments || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      });

      userOverdueAppointments.forEach((appointment) => {
        urgentTasks.push({
          id: `appointment-${appointment.id}`,
          title: `Overdue: ${appointment.title}`,
          dueDate: appointment.appointment_date,
          priority: 'high',
          type: 'appointment',
          entity_id: appointment.id,
          entity_type: 'appointment',
        });
      });

      // Get pending requests that need approval (for coordinators and directors)
      const userRole = await auth.getCurrentUserRole();
      if (userRole === 'Coordinator' || userRole === 'Director') {
        const { data: pendingRequests } = await supabase
          .from("requests")
          .select(`
            id,
            reference_number,
            status,
            created_at
          `)
          .eq("organization_id", userProfile.organizationId)
          .eq("status", "requested")
          .order("created_at", { ascending: true })
          .limit(5);

        (pendingRequests || []).forEach((request) => {
          urgentTasks.push({
            id: `request-${request.id}`,
            title: `Approval needed: ${request.reference_number || 'Service Request'}`,
            dueDate: request.created_at,
            priority: 'medium',
            type: 'approval',
            entity_id: request.id,
            entity_type: 'request',
          });
        });
      }

      return {
        success: true,
        data: urgentTasks.slice(0, 5), // Limit to 5 most urgent
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get user's workload summary
   */
  async getWorkloadSummary(): Promise<{
    success: boolean;
    data?: {
      activeCases: number;
      pendingRequests: number;
      upcomingAppointments: number;
      overdueItems: number;
    };
    error?: string;
  }> {
    try {
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();
      const today = new Date().toISOString().split('T')[0];

      // Get active cases count (simplified - would need proper case assignment logic)
      const { count: activeCases } = await supabase
        .from("case_files")
        .select("*", { count: "exact", head: true })
        .eq("organization_id", userProfile.organizationId)
        .eq("status", "active");

      // Get upcoming appointments count (next 7 days)
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const nextWeekStr = nextWeek.toISOString().split('T')[0];

      const { data: upcomingAppointmentsData } = await supabase
        .from("appointments")
        .select(`
          id,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            employees!appointment_assignments_employee_id_fkey(
              user_id
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .gte("appointment_date", today)
        .lte("appointment_date", nextWeekStr);

      const upcomingAppointments = (upcomingAppointmentsData || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      }).length;

      // Get pending requests count
      const { count: pendingRequestsCount } = await supabase
        .from("requests")
        .select("*", { count: "exact", head: true })
        .eq("organization_id", userProfile.organizationId)
        .eq("status", "requested");

      // Get overdue appointments for this calculation
      const { data: overdueAppointmentsData } = await supabase
        .from("appointments")
        .select(`
          id,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            employees!appointment_assignments_employee_id_fkey(
              user_id
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .lt("appointment_date", today)
        .in("status", ["planned", "confirmed"]);

      const userOverdueCount = (overdueAppointmentsData || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      }).length;

      // Calculate overdue items (overdue appointments + old pending requests)
      const overdueItems = userOverdueCount + Math.max(0, (pendingRequestsCount || 0) - 10);

      return {
        success: true,
        data: {
          activeCases: activeCases || 0,
          pendingRequests: pendingRequestsCount || 0,
          upcomingAppointments,
          overdueItems,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get recent case files for the current user
   */
  async getRecentCaseFiles(): Promise<{
    success: boolean;
    data?: RecentCaseFile[];
    error?: string;
  }> {
    try {
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();

      // Get recent case files (last 30 days or most recently updated)
      const { data: caseFiles } = await supabase
        .from("case_files")
        .select(`
          id,
          case_number,
          status,
          updated_at,
          case_file_contacts!case_file_contacts_case_file_id_fkey(
            contacts!case_file_contacts_contact_id_fkey(
              name
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .in("status", ["active", "opening"])
        .order("updated_at", { ascending: false })
        .limit(5);

      const recentCaseFiles: RecentCaseFile[] = (caseFiles || []).map((caseFile) => {
        // Get the primary contact name from the case file contacts
        const primaryContact = caseFile.case_file_contacts?.[0]?.contacts;
        const familyName = primaryContact?.name || "Unknown Family";

        const timeAgo = this.getTimeAgo(caseFile.updated_at);

        return {
          id: caseFile.id,
          title: familyName,
          lastUpdate: timeAgo,
          status: caseFile.status,
          case_number: caseFile.case_number,
        };
      });

      return {
        success: true,
        data: recentCaseFiles,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get recent notifications for the current user from the notifications table
   */
  async getRecentNotifications(): Promise<{
    success: boolean;
    data?: NotificationItem[];
    error?: string;
  }> {
    try {
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();

      // Get recent notifications from the notifications table
      const { data: notifications } = await supabase
        .from("notifications")
        .select(`
          id,
          title,
          message,
          type,
          read,
          created_at,
          data
        `)
        .eq("organization_id", userProfile.organizationId)
        .eq("user_id", currentUser.id)
        .order("created_at", { ascending: false })
        .limit(5);

      const notificationItems: NotificationItem[] = (notifications || []).map((notification) => {
        // Extract entity info from data JSONB field
        const data = notification.data as any;

        return {
          id: notification.id,
          title: notification.title,
          message: notification.message,
          timestamp: this.getTimeAgo(notification.created_at),
          type: notification.type as 'success' | 'warning' | 'info' | 'error',
          read: notification.read,
          entity_id: data?.entity_id || null,
          entity_type: data?.entity_type || null,
        };
      });

      return {
        success: true,
        data: notificationItems,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Helper method to format time ago
   */
  private getTimeAgo(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInHours < 1) {
      return "Il y a moins d'une heure";
    } else if (diffInHours < 24) {
      return `Il y a ${diffInHours}h`;
    } else if (diffInDays === 1) {
      return "Hier";
    } else if (diffInDays < 7) {
      return `Il y a ${diffInDays} jours`;
    } else {
      return date.toLocaleDateString('fr-CA');
    }
  }
}

export const dashboardService = DashboardService.getInstance();
