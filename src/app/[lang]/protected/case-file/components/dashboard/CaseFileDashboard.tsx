import { CaseFileDashboardData } from "../../lib/types";
import { FamilyInformation } from "./FamilyInformation";
import { ServiceRequirements } from "./ServiceRequirements";
import { QuickActions } from "./QuickActions";
import { AppointmentSummary } from "./AppointmentSummary";
import { AppointmentWithDetails } from "../../../scheduling/appointments/lib/types";

interface CaseFileDashboardProps {
  data: CaseFileDashboardData;
  appointments: AppointmentWithDetails[];
  lang: string;
  dictionary?: {
    title: string;
    quickActions: {
      title: string;
      schedule: string;
      addDocument: string;
      createNote: string;
      viewHistory: string;
    };
    familyInformation: {
      title: string;
      description: string;
    };
    serviceRequirements: {
      title: string;
      description: string;
    };
    appointmentSummary?: {
      title: string;
      today: string;
      tomorrow: string;
      upcoming: string;
      overdue: string;
      noAppointments: string;
      viewAll: string;
    };
  };
}

/**
 * Main dashboard component for active case files
 * Displays comprehensive case file information and management tools
 */
export function CaseFileDashboard({
  data,
  appointments,
  lang,
  dictionary,
}: CaseFileDashboardProps) {
  const { caseFile, familyInfo, serviceRequirements } = data;

  return (
    <div className="space-y-6">
      {/* Quick Actions */}
      <QuickActions caseFileId={caseFile.id} lang={lang} dictionary={dictionary?.quickActions} />

      {/* Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Family Information */}
        <FamilyInformation
          familyInfo={familyInfo}
          contacts={caseFile.contacts || []}
          caseFileId={caseFile.id}
          lang={lang}
          dictionary={dictionary?.familyInformation}
        />

        {/* Service Requirements */}
        <ServiceRequirements
          serviceRequirements={serviceRequirements}
          caseFile={caseFile}
          caseFileId={caseFile.id}
          lang={lang}
          dictionary={dictionary?.serviceRequirements}
        />

        {/* Appointment Summary */}
        <AppointmentSummary
          appointments={appointments}
          caseFile={caseFile}
          caseFileId={caseFile.id}
          lang={lang}
          dictionary={dictionary?.appointmentSummary}
        />
      </div>
    </div>
  );
}
