"use client";

import { useState, useTransition, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Calendar,
  Clock,
  Eye,
  Plus,
  X,
  Save,
  Loader2,
  AlertCircle,
  CheckCircle,
  Check,
  XCircle,
  Clock3,
  Ban,
  Users,
  UserPlus,
  Trash2,
} from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AppointmentWithDetails } from "../../../../scheduling/appointments/lib/types";
import {
  createCaseFileAppointment,
  updateCaseFileAppointment,
  updateAppointmentStatus,
} from "../../../actions/create-appointment";
import { checkEmployeeAvailabilityAction } from "../../../actions/check-availability";
import { checkRoomAvailabilityAction } from "../../../actions/check-room-availability";
import { getAppointmentAssignments } from "../../../actions/appointment-assignments";
import { getAppointmentRoom } from "../../../actions/appointment-rooms";
import { getOrganizationRooms, OrganizationRoom } from "../../../actions/get-organization-rooms";
import { Employee } from "@/app/[lang]/protected/employee/lib/types";
import { CaseFileWithDetails } from "../../../lib/case-file/types";

interface CaseFileAppointmentsProps {
  caseFileId: string;
  caseFile: CaseFileWithDetails;
  appointments: AppointmentWithDetails[];
  employees: Employee[];
  organizationId: string | null;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    noAppointments: string;
    noAppointmentsDescription: string;
    viewAppointment: string;
    scheduleNew: string;
  };
}

const defaultDictionary = {
  title: "Case File Appointments",
  description: "Appointments associated with this case file",
  noAppointments: "No appointments found",
  noAppointmentsDescription: "No appointments have been scheduled for this case file yet",
  viewAppointment: "View Appointment",
  scheduleNew: "Schedule New Appointment",
};

export function CaseFileAppointments({
  caseFileId,
  caseFile,
  appointments,
  employees,
  organizationId,
  lang: _lang,
  dictionary,
}: CaseFileAppointmentsProps) {
  const t = dictionary || defaultDictionary;
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [sidebarMode, setSidebarMode] = useState<"create" | "view" | "edit">("create");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [viewingAppointment, setViewingAppointment] = useState<AppointmentWithDetails | null>(null);
  const [appointmentForm, setAppointmentForm] = useState({
    title: "",
    description: "",
    start_time: "",
    end_time: "",
    service_id: "",
    room_id: "",
  });
  const [assignedEmployees, setAssignedEmployees] = useState<
    Array<{
      id: string;
      name: string;
      jobTitle?: string;
    }>
  >([]);
  const [selectedEmployee, setSelectedEmployee] = useState("");

  const [_availableEmployees, _setAvailableEmployees] = useState<
    Array<{
      id: string;
      name: string;
      jobTitle: string;
      department: string;
      isAvailable: boolean;
      conflicts?: string[];
    }>
  >([]);

  const [checkingAvailability, setCheckingAvailability] = useState(false);
  const [organizationRooms, setOrganizationRooms] = useState<OrganizationRoom[]>([]);
  const [loadingRooms, setLoadingRooms] = useState(true);
  const [currentAppointmentRoom, setCurrentAppointmentRoom] = useState<OrganizationRoom | null>(null);
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Get service information from case file
  const caseFileService = caseFile.request?.service || {
    id: "unknown",
    name: "Service Not Specified",
    description: "No service information available",
  };

  // Load organization rooms on component mount
  useEffect(() => {
    const loadRooms = async () => {
      if (!organizationId) return;

      try {
        setLoadingRooms(true);
        const result = await getOrganizationRooms(organizationId);
        if (result.success && result.data) {
          setOrganizationRooms(result.data);
        } else {
          console.error("Failed to load organization rooms:", result.error);
          setOrganizationRooms([]);
        }
      } catch (error) {
        console.error("Error loading organization rooms:", error);
        setOrganizationRooms([]);
      } finally {
        setLoadingRooms(false);
      }
    };

    loadRooms();
  }, [organizationId]);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  const handleViewAppointment = async (appointment: AppointmentWithDetails) => {
    setViewingAppointment(appointment);
    setSidebarMode("view");
    // Set the calendar to the appointment date
    setSelectedDate(new Date(appointment.appointment_date));

    // Load assignments and room for viewing
    if (organizationId) {
      try {
        // Load employee assignments
        const assignmentsResult = await getAppointmentAssignments(appointment.id, organizationId);
        if (assignmentsResult.success && assignmentsResult.data) {
          const existingAssignments = assignmentsResult.data.map((assignment: any) => ({
            id: assignment.employee_id,
            name: `${assignment.employees?.first_name || ''} ${assignment.employees?.last_name || ''}`.trim(),
            jobTitle: assignment.employees?.job_title || '',
          }));
          setAssignedEmployees(existingAssignments);
        } else {
          setAssignedEmployees([]);
        }

        // Load room assignment
        const roomResult = await getAppointmentRoom(appointment.id, organizationId);
        if (roomResult.success && roomResult.data) {
          const assignedRoom = organizationRooms.find(room => room.id === roomResult.data.room_id);
          setCurrentAppointmentRoom(assignedRoom || null);
        } else {
          setCurrentAppointmentRoom(null);
        }
      } catch (error) {
        console.error("Failed to load assignments for viewing:", error);
        setAssignedEmployees([]);
        setCurrentAppointmentRoom(null);
      }
    }

    setIsSidebarOpen(true);
  };

  const handleCreateAppointment = () => {
    setViewingAppointment(null);
    setSidebarMode("create");
    setSelectedDate(new Date());
    setAppointmentForm({
      title: "",
      description: "",
      start_time: "",
      end_time: "",
      service_id: caseFileService.id, // Auto-populate from case file service
      room_id: "",
    });
    setAssignedEmployees([]);
    setSelectedEmployee("");
    setIsSidebarOpen(true);
  };

  const handleEditAppointment = async (appointment: AppointmentWithDetails) => {
    setViewingAppointment(appointment);
    setSidebarMode("edit");
    setSelectedDate(new Date(appointment.appointment_date));
    setAppointmentForm({
      title: appointment.title,
      description: appointment.description || "",
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      service_id: caseFileService.id, // Auto-populate from case file service (same for all appointments)
      room_id: "", // Will be loaded from database
    });

    // Load existing assignments and room for this appointment
    if (organizationId) {
      try {
        // Load employee assignments
        const assignmentsResult = await getAppointmentAssignments(appointment.id, organizationId);
        if (assignmentsResult.success && assignmentsResult.data) {
          const existingAssignments = assignmentsResult.data.map((assignment: any) => ({
            id: assignment.employee_id,
            name: `${assignment.employees?.first_name || ''} ${assignment.employees?.last_name || ''}`.trim(),
            jobTitle: assignment.employees?.job_title || '',
          }));
          setAssignedEmployees(existingAssignments);
        }

        // Load room assignment
        const roomResult = await getAppointmentRoom(appointment.id, organizationId);
        if (roomResult.success && roomResult.data) {
          setAppointmentForm(prev => ({
            ...prev,
            room_id: roomResult.data.room_id || '',
          }));
        }
      } catch (error) {
        console.error("Failed to load existing assignments:", error);
      }
    }

    setIsSidebarOpen(true);
  };

  const handleFormChange = (field: string, value: string) => {
    setAppointmentForm((prev) => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  // Check room availability for the selected date and time
  const checkRoomAvailability = async (roomId: string): Promise<boolean> => {
    if (!selectedDate || !appointmentForm.start_time || !appointmentForm.end_time || !organizationId) {
      return false;
    }

    try {
      const result = await checkRoomAvailabilityAction(
        roomId,
        selectedDate.toISOString().split("T")[0],
        appointmentForm.start_time,
        appointmentForm.end_time,
        organizationId,
        sidebarMode === "edit" && viewingAppointment ? viewingAppointment.id : undefined
      );

      if (result.success) {
        return result.available;
      } else {
        console.warn("Room availability check not available, allowing booking:", result.error);
        return true; // Allow booking when availability data is not available
      }
    } catch (error) {
      console.warn("Error checking room availability, allowing booking:", error);
      return true; // Allow booking when availability check fails
    }
  };

  // Check employee availability for the selected date and time
  const checkEmployeeAvailability = async (employeeId: string): Promise<boolean> => {
    if (
      !selectedDate ||
      !appointmentForm.start_time ||
      !appointmentForm.end_time ||
      !organizationId
    ) {
      return false;
    }

    try {
      setCheckingAvailability(true);

      // Use server action for availability checking
      const result = await checkEmployeeAvailabilityAction(
        employeeId,
        selectedDate.toISOString().split("T")[0],
        appointmentForm.start_time,
        appointmentForm.end_time,
        organizationId,
        sidebarMode === "edit" && viewingAppointment ? viewingAppointment.id : undefined
      );

      if (result.success) {
        return result.available;
      } else {
        // If availability checking fails (e.g., no schedules set up), allow assignment
        // This is common in new systems where employee schedules aren't configured yet
        console.warn("Availability check not available, allowing assignment:", result.error);
        return true; // Allow assignment when availability data is not available
      }
    } catch (error) {
      console.warn("Error checking availability, allowing assignment:", error);
      return true; // Allow assignment when availability check fails
    } finally {
      setCheckingAvailability(false);
    }
  };

  const handleAddEmployee = async () => {
    if (!selectedEmployee) return;

    const employee = employees.find((emp) => emp.id === selectedEmployee);
    if (!employee) return;

    // Check if employee is already assigned
    if (assignedEmployees.some((emp) => emp.id === selectedEmployee)) {
      setError("Employee is already assigned to this appointment");
      return;
    }

    // Check if date and time are selected
    if (!selectedDate || !appointmentForm.start_time || !appointmentForm.end_time) {
      setError("Please select appointment date and time before assigning staff");
      return;
    }

    // Check employee availability
    const isAvailable = await checkEmployeeAvailability(selectedEmployee);

    if (!isAvailable) {
      const employeeName = `${employee.first_name} ${employee.last_name}`;

      // Check if we have conflict details from the availability check
      if (organizationId) {
        const result = await checkEmployeeAvailabilityAction(
          selectedEmployee,
          selectedDate.toISOString().split("T")[0],
          appointmentForm.start_time,
          appointmentForm.end_time,
          organizationId,
          sidebarMode === "edit" && viewingAppointment ? viewingAppointment.id : undefined
        );

        const errorMessage = result.conflictDetails
          ? `${employeeName} is not available. ${result.conflictDetails}`
          : `${employeeName} is not available for the selected date and time. Please choose a different employee or time slot.`;

        setError(errorMessage);
      } else {
        setError(`${employeeName} is not available for the selected date and time. Please choose a different employee or time slot.`);
      }
      return;
    }

    const newAssignment = {
      id: employee.id,
      name: `${employee.first_name} ${employee.last_name}`,
      jobTitle: employee.job_title,
    };

    setAssignedEmployees((prev) => [...prev, newAssignment]);
    setSelectedEmployee("");
    setError(null);
    setSuccess(`${newAssignment.name} has been assigned to this appointment`);

    // Clear success message after 3 seconds
    setTimeout(() => setSuccess(null), 3000);
  };

  const handleRemoveEmployee = (employeeId: string) => {
    setAssignedEmployees((prev) => prev.filter((emp) => emp.id !== employeeId));
  };

  // Helper functions for display
  const getServiceName = () => {
    return caseFileService.name;
  };

  const getRoomName = (roomId: string) => {
    return organizationRooms.find((r) => r.id === roomId)?.name || "No Room Selected";
  };

  const getRoomDetails = (roomId: string) => {
    const room = organizationRooms.find((r) => r.id === roomId);
    if (!room) return null;
    return {
      name: room.name,
      capacity: room.capacity,
      location: room.location_name,
      description: room.description,
    };
  };

  const handleStatusChange = async (
    appointmentId: string,
    newStatus:
      | "planned"
      | "confirmed"
      | "in_progress"
      | "completed"
      | "missed"
      | "postponed"
      | "cancelled"
  ) => {
    setError(null);
    setSuccess(null);

    startTransition(async () => {
      try {
        const result = await updateAppointmentStatus(appointmentId, newStatus);

        if (result.success) {
          setSuccess(result.message || `Appointment marked as ${newStatus}`);
          // Close sidebar after a brief delay to show success message
          setTimeout(() => {
            setIsSidebarOpen(false);
            setSuccess(null);
            // Refresh the page to show the updated appointment
            window.location.reload();
          }, 1500);
        } else {
          setError(result.error || `Failed to update appointment status`);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const handleSaveAppointment = async () => {
    // Clear previous messages
    setError(null);
    setSuccess(null);

    // Basic validation
    if (!appointmentForm.title.trim()) {
      setError("Title is required");
      return;
    }
    if (!selectedDate) {
      setError("Please select a date");
      return;
    }
    if (!appointmentForm.start_time) {
      setError("Start time is required");
      return;
    }
    if (!appointmentForm.end_time) {
      setError("End time is required");
      return;
    }

    // Validate time logic
    if (appointmentForm.end_time <= appointmentForm.start_time) {
      setError("End time must be after start time");
      return;
    }

    // Validate room availability if a room is selected
    if (appointmentForm.room_id && organizationId) {
      const isRoomAvailable = await checkRoomAvailability(appointmentForm.room_id);
      if (!isRoomAvailable) {
        // Get detailed error message
        const result = await checkRoomAvailabilityAction(
          appointmentForm.room_id,
          selectedDate.toISOString().split("T")[0],
          appointmentForm.start_time,
          appointmentForm.end_time,
          organizationId,
          sidebarMode === "edit" && viewingAppointment ? viewingAppointment.id : undefined
        );

        const roomName = getRoomName(appointmentForm.room_id);
        const errorMessage = result.conflictDetails
          ? `${roomName} is not available. ${result.conflictDetails}`
          : `${roomName} is not available for the selected date and time. Please choose a different room or time slot.`;

        setError(errorMessage);
        return;
      }
    }

    startTransition(async () => {
      try {
        // Create FormData for the appointment
        const formData = new FormData();
        formData.append("title", appointmentForm.title.trim());
        formData.append("description", appointmentForm.description.trim());
        formData.append("appointment_date", selectedDate.toISOString().split("T")[0]);
        formData.append("start_time", appointmentForm.start_time);
        formData.append("end_time", appointmentForm.end_time);

        if (sidebarMode === "edit" && viewingAppointment) {
          formData.append("appointment_id", viewingAppointment.id);
        } else {
          formData.append("status", "planned");
          formData.append("case_file_id", caseFileId);
        }

        // Prepare assignments data
        const assignments = assignedEmployees.map(employee => ({
          employeeId: employee.id,
          assignmentRole: "primary", // Default role since we removed role selection
        }));

        const result =
          sidebarMode === "edit"
            ? await updateCaseFileAppointment(formData, assignments, appointmentForm.room_id || undefined)
            : await createCaseFileAppointment(formData, assignments, appointmentForm.room_id || undefined);

        if (result.success) {
          setSuccess(
            sidebarMode === "edit"
              ? "Appointment updated successfully!"
              : "Appointment created successfully!"
          );
          // Reset form
          setAppointmentForm({
            title: "",
            description: "",
            start_time: "",
            end_time: "",
            service_id: caseFileService.id,
            room_id: "",
          });
          setSelectedDate(new Date());
          setAssignedEmployees([]);
          setSelectedEmployee("");
          // Close sidebar after a brief delay to show success message
          setTimeout(() => {
            setIsSidebarOpen(false);
            setSuccess(null);
            // Refresh the page to show the updated appointment
            window.location.reload();
          }, 1500);
        } else {
          setError(
            result.error || `Failed to ${sidebarMode === "edit" ? "update" : "create"} appointment`
          );
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "missed":
        return "bg-orange-100 text-orange-800";
      case "postponed":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex">
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`transition-all duration-500 ease-in-out ${isSidebarOpen ? "flex-1 mr-6" : "w-full"}`}
      >
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                <CardTitle>
                  {isSidebarOpen
                    ? sidebarMode === "create"
                      ? "New Appointment Details"
                      : sidebarMode === "edit"
                        ? "Edit Appointment Details"
                        : "Appointment Details"
                    : t.title}
                </CardTitle>
              </div>
              {!isSidebarOpen && (
                <Button size="sm" onClick={handleCreateAppointment}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t.scheduleNew}
                </Button>
              )}
            </div>
            <p className="text-sm text-muted-foreground">
              {isSidebarOpen
                ? sidebarMode === "create"
                  ? "Fill in the appointment details below"
                  : sidebarMode === "edit"
                    ? "Update the appointment details below"
                    : "View appointment details and information"
                : t.description}
            </p>
          </CardHeader>
          <CardContent>
            {isSidebarOpen ? (
              sidebarMode === "create" || sidebarMode === "edit" ? (
                // Appointment Form (Create/Edit)
                <div className="space-y-4">
                  {/* Error/Success Messages */}
                  {error && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  {success && (
                    <Alert className="border-green-200 bg-green-50 text-green-800">
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>{success}</AlertDescription>
                    </Alert>
                  )}

                  <div className="grid gap-3">
                    {/* Basic Information */}
                    <div>
                      <Label htmlFor="title" className="text-sm font-medium">
                        Appointment Title *
                      </Label>
                      <Input
                        id="title"
                        value={appointmentForm.title}
                        onChange={(e) => handleFormChange("title", e.target.value)}
                        placeholder="Enter appointment title"
                        disabled={isPending}
                        className="mt-1"
                      />
                    </div>

                    <div>
                      <Label htmlFor="description" className="text-sm font-medium">
                        Description
                      </Label>
                      <Textarea
                        id="description"
                        value={appointmentForm.description}
                        onChange={(e) => handleFormChange("description", e.target.value)}
                        placeholder="Enter appointment description"
                        rows={2}
                        disabled={isPending}
                        className="mt-1 resize-none"
                      />
                    </div>

                    {/* Service Selection - Pre-populated from Case File */}
                    <div>
                      <Label className="text-sm font-medium">Service Type (from Case File)</Label>
                      <div className="mt-1 p-2 bg-muted rounded-md border">
                        <div className="font-medium text-sm">{getServiceName()}</div>
                        <div className="text-xs text-muted-foreground">
                          {caseFileService.description}
                        </div>
                      </div>
                      <p className="text-xs text-muted-foreground mt-1">
                        Service type is automatically set from the case file and cannot be changed.
                      </p>
                    </div>

                    {/* Time Selection */}
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <Label htmlFor="start_time" className="text-sm font-medium">
                          Start Time *
                        </Label>
                        <Input
                          id="start_time"
                          type="time"
                          value={appointmentForm.start_time}
                          onChange={(e) => handleFormChange("start_time", e.target.value)}
                          disabled={isPending}
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="end_time" className="text-sm font-medium">
                          End Time *
                        </Label>
                        <Input
                          id="end_time"
                          type="time"
                          value={appointmentForm.end_time}
                          onChange={(e) => handleFormChange("end_time", e.target.value)}
                          disabled={isPending}
                          className="mt-1"
                        />
                      </div>
                    </div>

                    {/* Room Selection */}
                    <div>
                      <Label className="text-sm font-medium">Room</Label>
                      <Select
                        value={appointmentForm.room_id}
                        onValueChange={(value) => handleFormChange("room_id", value)}
                        disabled={isPending}
                      >
                        <SelectTrigger className="mt-1">
                          <SelectValue placeholder="Select a room" />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingRooms ? (
                            <SelectItem value="loading" disabled>
                              Loading rooms...
                            </SelectItem>
                          ) : organizationRooms.length === 0 ? (
                            <SelectItem value="no-rooms" disabled>
                              No rooms available
                            </SelectItem>
                          ) : (
                            organizationRooms.map((room) => (
                              <SelectItem key={room.id} value={room.id}>
                                <div>
                                  <div className="font-medium">{room.name}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {room.location_name} • Capacity: {room.capacity || "N/A"}
                                    {room.description && ` • ${room.description}`}
                                  </div>
                                </div>
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Staff Assignment Section */}
                    <div className="space-y-3 pt-2 border-t">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <Label className="text-sm font-medium">Staff Assignment</Label>
                      </div>

                      {/* Add Employee Form */}
                      <div className="flex gap-2">
                        <Select
                          value={selectedEmployee}
                          onValueChange={setSelectedEmployee}
                          disabled={isPending}
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Select employee to assign" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees.length === 0 ? (
                              <SelectItem value="no-employees" disabled>
                                No employees found
                              </SelectItem>
                            ) : (
                              employees.map((employee) => (
                                <SelectItem key={employee.id} value={employee.id}>
                                  <div>
                                    <div className="font-medium">
                                      {employee.first_name} {employee.last_name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {employee.job_title || "No title"}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>

                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleAddEmployee}
                          disabled={isPending || !selectedEmployee || checkingAvailability}
                          className="h-10"
                        >
                          {checkingAvailability ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <UserPlus className="h-4 w-4" />
                          )}
                        </Button>
                      </div>

                      {/* Assigned Staff List */}
                      {assignedEmployees.length > 0 && (
                        <div className="space-y-2">
                          <Label className="text-xs font-medium text-muted-foreground">
                            Assigned Staff ({assignedEmployees.length})
                          </Label>
                          <div className="space-y-2">
                            {assignedEmployees.map((employee) => (
                              <div
                                key={employee.id}
                                className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                              >
                                <div className="flex-1">
                                  <div className="font-medium text-sm">{employee.name}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {employee.jobTitle}
                                  </div>
                                </div>
                                <div className="flex items-center gap-1">
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleRemoveEmployee(employee.id)}
                                    disabled={isPending}
                                    className="h-7 w-7 p-0 text-muted-foreground hover:text-destructive"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {assignedEmployees.length === 0 && (
                        <div className="text-center py-3 text-sm text-muted-foreground">
                          No staff assigned yet. Select an employee above to assign.
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end gap-2 pt-3 border-t">
                    <Button
                      variant="outline"
                      onClick={() => setIsSidebarOpen(false)}
                      disabled={isPending}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleSaveAppointment} disabled={isPending}>
                      {isPending ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          {sidebarMode === "edit" ? "Updating..." : "Creating..."}
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          {sidebarMode === "edit" ? "Update Appointment" : "Save Appointment"}
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                // Appointment Details View
                <div className="space-y-4">
                  {viewingAppointment && (
                    <>
                      {/* Service Type as Header */}
                      <div className="pb-2 border-b">
                        <Badge variant="secondary" className="mb-2">
                          {getServiceName()} {/* From case file */}
                        </Badge>
                        <h3 className="text-lg font-medium">{viewingAppointment.title}</h3>
                        {viewingAppointment.description && (
                          <p className="text-sm text-muted-foreground mt-1">
                            {viewingAppointment.description}
                          </p>
                        )}
                      </div>

                      {/* Date and Time */}
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Date</Label>
                          <p className="text-sm font-medium">{viewingAppointment.date_display}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Status
                          </Label>
                          <Badge className={getStatusColor(viewingAppointment.status)}>
                            {viewingAppointment.status_display || viewingAppointment.status}
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Start Time
                          </Label>
                          <p className="text-sm font-medium">{viewingAppointment.start_time}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            End Time
                          </Label>
                          <p className="text-sm font-medium">{viewingAppointment.end_time}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Duration
                          </Label>
                          <p className="text-sm font-medium">
                            {viewingAppointment.duration_display}
                          </p>
                        </div>
                      </div>

                      {/* Room Information */}
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Room</Label>
                        <div className="mt-1 p-2 bg-muted rounded-md">
                          {currentAppointmentRoom ? (
                            <>
                              <div className="font-medium text-sm">{currentAppointmentRoom.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {currentAppointmentRoom.location_name} • Capacity: {currentAppointmentRoom.capacity || "N/A"}
                                {currentAppointmentRoom.description && ` • ${currentAppointmentRoom.description}`}
                              </div>
                            </>
                          ) : (
                            <div className="text-sm text-muted-foreground">No room assigned</div>
                          )}
                        </div>
                      </div>

                      {/* Assigned Staff Section */}
                      <div>
                        <div className="flex items-center gap-2 mb-2">
                          <Users className="h-4 w-4" />
                          <Label className="text-sm font-medium text-muted-foreground">Assigned Staff</Label>
                        </div>
                        {assignedEmployees.length > 0 ? (
                          <div className="space-y-2">
                            {assignedEmployees.map((employee) => (
                              <div
                                key={employee.id}
                                className="flex items-center p-2 bg-muted/50 rounded-md"
                              >
                                <div className="flex-1">
                                  <div className="font-medium text-sm">{employee.name}</div>
                                  <div className="text-xs text-muted-foreground">
                                    {employee.jobTitle}
                                  </div>
                                </div>
                                <Badge variant="secondary" className="text-xs">
                                  Primary
                                </Badge>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <div className="text-center py-3 text-sm text-muted-foreground bg-muted/30 rounded-md">
                            No staff assigned to this appointment
                          </div>
                        )}
                      </div>

                      {/* Status Change Actions */}
                      {viewingAppointment.status === "planned" ||
                      viewingAppointment.status === "confirmed" ? (
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Quick Actions
                          </Label>
                          <div className="mt-2 grid grid-cols-2 gap-2">
                            <Button
                              size="sm"
                              className="bg-green-600 hover:bg-green-700 text-white"
                              onClick={() => handleStatusChange(viewingAppointment.id, "completed")}
                              disabled={isPending}
                            >
                              <Check className="h-4 w-4 mr-2" />
                              Complete
                            </Button>
                            <Button
                              size="sm"
                              className="bg-orange-600 hover:bg-orange-700 text-white"
                              onClick={() => handleStatusChange(viewingAppointment.id, "missed")}
                              disabled={isPending}
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              Mark Missed
                            </Button>
                            <Button
                              size="sm"
                              className="bg-purple-600 hover:bg-purple-700 text-white"
                              onClick={() => handleStatusChange(viewingAppointment.id, "postponed")}
                              disabled={isPending}
                            >
                              <Clock3 className="h-4 w-4 mr-2" />
                              Postpone
                            </Button>
                            <Button
                              size="sm"
                              className="bg-red-600 hover:bg-red-700 text-white"
                              onClick={() => handleStatusChange(viewingAppointment.id, "cancelled")}
                              disabled={isPending}
                            >
                              <Ban className="h-4 w-4 mr-2" />
                              Cancel
                            </Button>
                          </div>
                        </div>
                      ) : (
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">
                            Status
                          </Label>
                          <p className="text-sm mt-1">
                            This appointment is{" "}
                            {viewingAppointment.status_display || viewingAppointment.status}. Status
                            changes are only available for planned or confirmed appointments.
                          </p>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex justify-end gap-2 pt-3 border-t">
                        <Button variant="outline" onClick={() => setIsSidebarOpen(false)}>
                          Close
                        </Button>
                        <Button onClick={() => handleEditAppointment(viewingAppointment)}>
                          <Save className="h-4 w-4 mr-2" />
                          Edit Appointment
                        </Button>
                      </div>
                    </>
                  )}
                </div>
              )
            ) : appointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">{t.noAppointments}</h3>
                <p className="text-muted-foreground mb-4">{t.noAppointmentsDescription}</p>
                <Button onClick={handleCreateAppointment}>
                  <Plus className="h-4 w-4 mr-2" />
                  {t.scheduleNew}
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {appointments
                  .sort((a, b) => {
                    // Sort by most recent: first by date (desc), then by time (desc)
                    const dateCompare = new Date(b.appointment_date).getTime() - new Date(a.appointment_date).getTime();
                    if (dateCompare !== 0) return dateCompare;
                    return b.start_time.localeCompare(a.start_time);
                  })
                  .map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="font-medium">
                          {caseFileService.name}
                        </h4>
                        <Badge className={getStatusColor(appointment.status)}>
                          {appointment.status_display || appointment.status}
                        </Badge>
                      </div>

                      {/* Date, Time, Duration */}
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          {appointment.date_display}
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4" />
                          {appointment.time_display}
                        </div>
                        <div>{appointment.duration_display}</div>
                      </div>

                      {/* Room, Staff */}
                      <div className="flex items-center gap-4 text-sm">
                        {/* Room */}
                        {appointment.appointment_rooms && appointment.appointment_rooms.length > 0 && appointment.appointment_rooms[0].rooms && (
                          <div className="flex items-center gap-1 text-green-600">
                            <span className="font-medium">Room:</span>
                            <span>{appointment.appointment_rooms[0].rooms.name}</span>
                            {appointment.appointment_rooms[0].rooms.locations?.name && (
                              <span className="text-muted-foreground">({appointment.appointment_rooms[0].rooms.locations.name})</span>
                            )}
                          </div>
                        )}

                        {/* Staff */}
                        {appointment.appointment_assignments && appointment.appointment_assignments.length > 0 && (
                          <div className="flex items-center gap-1 text-purple-600">
                            <span className="font-medium">Staff:</span>
                            <span>
                              {appointment.appointment_assignments
                                .filter(assignment => assignment.employees)
                                .slice(0, 2)
                                .map(assignment => `${assignment.employees?.first_name} ${assignment.employees?.last_name}`)
                                .join(", ")}
                              {appointment.appointment_assignments.filter(a => a.employees).length > 2 &&
                                ` +${appointment.appointment_assignments.filter(a => a.employees).length - 2} more`
                              }
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewAppointment(appointment)}
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      {t.viewAppointment}
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Secondary Sidebar for Appointment Creation */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="w-96 bg-background border-l shadow-lg flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">
                {sidebarMode === "create"
                  ? "Select Date"
                  : sidebarMode === "edit"
                    ? "Change Date"
                    : "Appointment Date"}
              </h2>
              <Button variant="ghost" size="sm" onClick={() => setIsSidebarOpen(false)}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Calendar Content */}
            <div className="flex-1 p-3 overflow-y-auto">
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground">
                  {sidebarMode === "create"
                    ? "Select a date for your appointment"
                    : sidebarMode === "edit"
                      ? "Change the appointment date"
                      : "Appointment scheduled for this date"}
                </div>

                <CalendarComponent
                  mode="single"
                  selected={selectedDate}
                  onSelect={
                    sidebarMode === "create" || sidebarMode === "edit"
                      ? handleDateSelect
                      : undefined
                  }
                  className="rounded-md border w-full"
                  disabled={sidebarMode === "view"}
                />

                {selectedDate && (
                  <div className="p-2 bg-muted rounded text-center">
                    <p className="text-xs font-medium">
                      {sidebarMode === "create"
                        ? "Selected:"
                        : sidebarMode === "edit"
                          ? "New Date:"
                          : "Scheduled:"}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedDate.toLocaleDateString()}
                    </p>
                  </div>
                )}

                <div className="text-xs text-muted-foreground text-center">
                  {sidebarMode === "create"
                    ? "💡 Pick a date, then fill the form"
                    : sidebarMode === "edit"
                      ? "✏️ Select new date or keep current"
                      : "📅 Appointment date shown above"}
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
