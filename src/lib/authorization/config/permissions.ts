/**
 * Centralized permission registry for the application
 *
 * Permissions follow a strict 3-level structure: domain:feature:action
 * - domain: The high-level domain (e.g., user, organization)
 * - feature: The specific feature within the domain (e.g., profile, management)
 * - action: The action being performed (e.g., read, update)
 *
 * Wildcards:
 * - domain:feature:* - All actions within a feature
 * - domain:* - All features and actions within a domain
 * - * - All permissions across all domains
 */

import CONTACT_SECURITY from "@/app/[lang]/protected/contact/(features)/management/lib/security/permissions";
import { DOMAIN_PERMISSIONS as REQUEST_PERMISSIONS } from "@/app/[lang]/protected/request/lib/config/domain";
import { DOCUMENT_PERMISSIONS } from "@/app/[lang]/protected/document/lib/security";
import { DOMAIN_PERMISSIONS as CASE_FILE_PERMISSIONS } from "@/app/[lang]/protected/case-file/lib/config/domain";

import { MANAGEMENT_PERMISSIONS as EMPLOYEE_MANAGEMENT_PERMISSIONS } from "@/app/[lang]/protected/employee/(features)/management/lib/security/permissions";
import { AVAILABILITY_PERMISSIONS } from "@/app/[lang]/protected/employee/(features)/availability/lib/security";

export const PERMISSIONS = {
  // Dashboard domain
  DASHBOARD: {
    VIEW: "dashboard:view",
    ALL: "dashboard:*",
  },

  // User domain
  USER: {
    PROFILE: {
      READ: "user:profile:read",
      UPDATE: "user:profile:update",
      ALL: "user:profile:*",
    },
    MANAGEMENT: {
      LIST: "user:management:list",
      CREATE: "user:management:create",
      READ: "user:management:read",
      UPDATE: "user:management:update",
      DELETE: "user:management:delete",
      ALL: "user:management:*",
    },
    ALL: "user:*",
  },

  // Organization domain
  ORGANIZATION: {
    PROFILE: {
      READ: "organization:profile:read",
      UPDATE: "organization:profile:update",
      MANAGE: "organization:profile:manage",
      ALL: "organization:profile:*",
    },
    SYSTEM_ADMIN: {
      LIST: "organization:system-admin:list",
      CREATE: "organization:system-admin:create",
      READ: "organization:system-admin:read",
      UPDATE: "organization:system-admin:update",
      DELETE: "organization:system-admin:delete",
      ALL: "organization:system-admin:*",
    },
    ALL: "organization:*",
  },
  CONTACT: {
    MANAGEMENT: CONTACT_SECURITY.permissions,
  },
  EMPLOYEE: {
    MANAGEMENT: EMPLOYEE_MANAGEMENT_PERMISSIONS,
    AVAILABILITY: AVAILABILITY_PERMISSIONS,
  },

  // Request domain
  REQUEST: {
    MANAGEMENT: REQUEST_PERMISSIONS,
  },

  // Document domain
  DOCUMENT: DOCUMENT_PERMISSIONS,

  // Case File domain
  CASE_FILE: CASE_FILE_PERMISSIONS,

  // Global wildcard
  ALL: "*",
};
// Type for type-safety
export type PermissionKey = typeof PERMISSIONS;
