import type { CaseFileWithDetails } from "../types";
import { createSupabaseClient } from "../../shared/database/supabaseClient";
import { getCaseFile } from "./getCaseFile";
import { getEmployeeOptional } from "../../employee/queries/getEmployee";
import { getContactsByCaseFile } from "../../contact/queries/getContact";
import { toCaseFileWithDetails } from "../transformers/toCaseFileWithDetails";
import { ProfileService } from "@/app/[lang]/protected/organization/(features)/profile/lib/services/ProfileService";

/**
 * Get request data for a case file
 * @param supabase Supabase client
 * @param requestId Request ID
 * @returns Request data with service and location
 */
const getRequestData = async (supabase: any, requestId: string) => {
  // Get current organization for security
  const organization = await ProfileService.getCurrentOrganization();
  if (!organization) {
    throw new Error("Organization not found");
  }

  const { data, error } = await supabase
    .from("requests")
    .select(`
      id,
      reference_number,
      start_date,
      end_date,
      duration,
      periodicity,
      frequency_count,
      service:service_id(
        id,
        name,
        description
      ),
      location:location_id(
        id,
        name,
        address
      )
    `)
    .eq("id", requestId)
    .eq("organization_id", organization.id)
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      return null; // Request not found
    }
    throw new Error(`Failed to get request: ${error.message}`);
  }

  return data;
};

/**
 * Get case file with full details including employee and contacts
 * @param id Case file ID
 * @returns Case file with all related data
 */
export const getCaseFileWithDetails = async (id: string): Promise<CaseFileWithDetails> => {
  const supabase = await createSupabaseClient();

  // Get core case file data
  const caseFile = await getCaseFile(supabase, id);

  // Get related data in parallel
  const [employee, contacts, request] = await Promise.all([
    caseFile.assigned_to ? getEmployeeOptional(supabase, caseFile.assigned_to) : null,
    getContactsByCaseFile(supabase, id),
    caseFile.request_id ? getRequestData(supabase, caseFile.request_id) : null,
  ]);

  // Transform to domain model
  return toCaseFileWithDetails({
    caseFile,
    employee,
    contacts,
    request,
  });
};

/**
 * Get case file with details and specific includes
 * @param id Case file ID
 * @param options What to include in the response
 * @returns Case file with requested related data
 */
export const getCaseFileWithDetailsOptional = async (
  id: string,
  options: {
    includeEmployee?: boolean;
    includeContacts?: boolean;
  } = {}
): Promise<CaseFileWithDetails> => {
  const { includeEmployee = true, includeContacts = true } = options;

  const supabase = await createSupabaseClient();

  // Get core case file data
  const caseFile = await getCaseFile(supabase, id);

  // Get related data based on options
  const [employee, contacts] = await Promise.all([
    includeEmployee && caseFile.assigned_to
      ? getEmployeeOptional(supabase, caseFile.assigned_to)
      : null,
    includeContacts ? getContactsByCaseFile(supabase, id) : [],
  ]);

  // Transform to domain model
  return toCaseFileWithDetails({
    caseFile,
    employee,
    contacts,
  });
};
