"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { Tabs, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ReactNode } from "react";

interface CaseFileTabsProps {
  activeTab: string;
  caseFileId: string;
  lang: string;
  children: ReactNode;
  dictionary: {
    dashboard: string;
    documents: string;
    contacts: string;
    appointments: string;
    history: string;
  };
}

/**
 * Client-side tabs component for case file navigation
 * Handles URL updates when tabs are changed
 */
export function CaseFileTabs({
  activeTab,
  caseFileId,
  lang,
  children,
  dictionary,
}: CaseFileTabsProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleTabChange = (newTab: string) => {
    // Create new search params
    const params = new URLSearchParams(searchParams);

    if (newTab === "dashboard") {
      // Remove tab param for dashboard (default)
      params.delete("tab");
    } else {
      // Set tab param for other tabs
      params.set("tab", newTab);
    }

    // Build new URL
    const newUrl = `/${lang}/protected/case-file/${caseFileId}/active${
      params.toString() ? `?${params.toString()}` : ""
    }`;

    // Navigate to new URL
    router.push(newUrl);
  };

  return (
    <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
      <TabsList className="grid w-full grid-cols-5">
        <TabsTrigger value="dashboard">{dictionary.dashboard}</TabsTrigger>
        <TabsTrigger value="documents">{dictionary.documents}</TabsTrigger>
        <TabsTrigger value="contacts">{dictionary.contacts}</TabsTrigger>
        <TabsTrigger value="appointments">{dictionary.appointments}</TabsTrigger>
        <TabsTrigger value="history">{dictionary.history}</TabsTrigger>
      </TabsList>
      {children}
    </Tabs>
  );
}
