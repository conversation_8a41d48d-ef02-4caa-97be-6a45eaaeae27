"use server";

import { EmployeeAvailabilityService } from "@/services/scheduling/EmployeeAvailabilityService";

export async function checkEmployeeAvailabilityAction(
  employeeId: string,
  date: string,
  startTime: string,
  endTime: string,
  organizationId: string
): Promise<{ success: boolean; available: boolean; error?: string }> {
  try {
    const availabilityService = new EmployeeAvailabilityService();
    const result = await availabilityService.isEmployeeAvailable(
      employeeId,
      date,
      startTime,
      endTime,
      organizationId
    );

    if (result.success) {
      return {
        success: true,
        available: result.data || false,
      };
    } else {
      return {
        success: false,
        available: false,
        error: typeof result.error === 'string' ? result.error : "Failed to check availability",
      };
    }
  } catch (error) {
    return {
      success: false,
      available: false,
      error: error instanceof Error ? error.message : "Failed to check availability",
    };
  }
}
