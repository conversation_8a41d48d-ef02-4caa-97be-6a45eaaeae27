import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { P } from "@/components/typography";
import { Calendar, Clock, MapPin, <PERSON><PERSON><PERSON>riangle, CheckCircle, ArrowRight } from "lucide-react";
import { AppointmentWithDetails } from "../../../scheduling/appointments/lib/types";
import Link from "next/link";

interface AppointmentSummaryProps {
  appointments: AppointmentWithDetails[];
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    today: string;
    tomorrow: string;
    upcoming: string;
    overdue: string;
    noAppointments: string;
    viewAll: string;
  };
}

/**
 * Appointment Summary component for case file dashboard
 * Shows today's appointments, upcoming appointments, and overdue items
 */
export function AppointmentSummary({
  appointments,
  caseFileId,
  lang,
  dictionary,
}: AppointmentSummaryProps) {
  // Default translations
  const defaultDictionary = {
    title: "Appointments Summary",
    today: "Today",
    tomorrow: "Tomorrow",
    upcoming: "Upcoming",
    overdue: "Overdue",
    noAppointments: "No upcoming appointments",
    viewAll: "View All Appointments",
  };

  const t = dictionary || defaultDictionary;

  // Get current date for filtering
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  // Filter appointments by date
  const todayAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.appointment_date);
    aptDate.setHours(0, 0, 0, 0);
    return aptDate.getTime() === today.getTime();
  });

  const tomorrowAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.appointment_date);
    aptDate.setHours(0, 0, 0, 0);
    return aptDate.getTime() === tomorrow.getTime();
  });

  const upcomingAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.appointment_date);
    aptDate.setHours(0, 0, 0, 0);
    return aptDate.getTime() > tomorrow.getTime();
  }).slice(0, 2); // Show next 2 upcoming

  const overdueAppointments = appointments.filter(apt => {
    const aptDate = new Date(apt.appointment_date);
    aptDate.setHours(0, 0, 0, 0);
    return aptDate.getTime() < today.getTime() && 
           (apt.status === "planned" || apt.status === "confirmed");
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "missed":
        return "bg-orange-100 text-orange-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatTime = (time: string) => {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString([], { 
      hour: 'numeric', 
      minute: '2-digit' 
    });
  };

  const AppointmentItem = ({ appointment, label }: { appointment: AppointmentWithDetails; label: string }) => (
    <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          {label}
        </div>
        <div>
          <P className="font-medium text-sm">{appointment.title}</P>
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {formatTime(appointment.start_time)}
            <MapPin className="h-3 w-3" />
            Room {appointment.id.slice(-1)} {/* Mock room display */}
          </div>
        </div>
      </div>
      <Badge className={`text-xs ${getStatusColor(appointment.status)}`}>
        {appointment.status}
      </Badge>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Calendar className="h-5 w-5 mr-2" />
            {t.title}
          </div>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=appointments`}>
              {t.viewAll}
              <ArrowRight className="h-4 w-4 ml-2" />
            </Link>
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Today's Appointments */}
        {todayAppointments.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <P className="font-medium text-sm">{t.today}</P>
            </div>
            <div className="space-y-2">
              {todayAppointments.map(appointment => (
                <AppointmentItem 
                  key={appointment.id} 
                  appointment={appointment} 
                  label={formatTime(appointment.start_time)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Tomorrow's Appointments */}
        {tomorrowAppointments.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Calendar className="h-4 w-4 text-blue-600" />
              <P className="font-medium text-sm">{t.tomorrow}</P>
            </div>
            <div className="space-y-2">
              {tomorrowAppointments.map(appointment => (
                <AppointmentItem 
                  key={appointment.id} 
                  appointment={appointment} 
                  label={formatTime(appointment.start_time)}
                />
              ))}
            </div>
          </div>
        )}

        {/* Upcoming Appointments */}
        {upcomingAppointments.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Clock className="h-4 w-4 text-purple-600" />
              <P className="font-medium text-sm">{t.upcoming}</P>
            </div>
            <div className="space-y-2">
              {upcomingAppointments.map(appointment => (
                <AppointmentItem 
                  key={appointment.id} 
                  appointment={appointment} 
                  label={new Date(appointment.appointment_date).toLocaleDateString()}
                />
              ))}
            </div>
          </div>
        )}

        {/* Overdue Appointments */}
        {overdueAppointments.length > 0 && (
          <div>
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <P className="font-medium text-sm text-orange-600">{t.overdue}</P>
            </div>
            <div className="space-y-2">
              {overdueAppointments.map(appointment => (
                <AppointmentItem 
                  key={appointment.id} 
                  appointment={appointment} 
                  label={new Date(appointment.appointment_date).toLocaleDateString()}
                />
              ))}
            </div>
          </div>
        )}

        {/* No Appointments */}
        {appointments.length === 0 && (
          <div className="text-center py-6">
            <Calendar className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <P className="text-muted-foreground">{t.noAppointments}</P>
          </div>
        )}

        {/* Summary Stats */}
        {appointments.length > 0 && (
          <div className="pt-3 border-t">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <P className="text-lg font-semibold">{todayAppointments.length}</P>
                <P className="text-xs text-muted-foreground">Today</P>
              </div>
              <div>
                <P className="text-lg font-semibold">{upcomingAppointments.length + tomorrowAppointments.length}</P>
                <P className="text-xs text-muted-foreground">Upcoming</P>
              </div>
              <div>
                <P className="text-lg font-semibold text-orange-600">{overdueAppointments.length}</P>
                <P className="text-xs text-muted-foreground">Overdue</P>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
