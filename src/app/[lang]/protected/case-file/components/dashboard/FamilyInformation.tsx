import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { P } from "@/components/typography";
import { Users, Phone, Mail, ArrowRight, User, Baby } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";
import Link from "next/link";

interface FamilyInformationProps {
  familyInfo: CaseFileDashboardData["familyInfo"];
  contacts: Array<{
    id: string;
    name: string;
    email?: any;
    phone?: any;
    relationshipType: string;
  }>;
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    contacts?: string;
    children?: string;
    adults?: string;
    viewAllContacts?: string;
    noContacts?: string;
  };
}

/**
 * Family Information component for case file dashboard
 * Displays case file contacts organized by relationship type
 */
export function FamilyInformation({
  familyInfo,
  contacts,
  caseFileId,
  lang,
  dictionary,
}: FamilyInformationProps) {
  // Default translations
  const defaultDictionary = {
    title: "Family Information",
    description: "Contact details and relationships",
    contacts: "Contacts",
    children: "Children",
    adults: "Adults",
    viewAllContacts: "View All Contacts",
    noContacts: "No contacts added",
  };

  const t = {
    ...defaultDictionary,
    ...dictionary,
  };

  // Helper functions
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const formatPhone = (phone: string) => {
    if (!phone) return null;
    // Simple phone formatting for display
    return phone.replace(/(\d{3})(\d{3})(\d{4})/, "($1) $2-$3");
  };

  // Organize contacts by type
  const children = contacts.filter((contact) => contact.relationshipType === "child");
  const adults = contacts.filter(
    (contact) =>
      contact.relationshipType === "parent" ||
      contact.relationshipType === "guardian" ||
      contact.relationshipType === "grandparent" ||
      contact.relationshipType === "sibling"
  );
  const professionals = contacts.filter(
    (contact) =>
      contact.relationshipType === "social_worker" ||
      contact.relationshipType === "therapist" ||
      contact.relationshipType === "teacher" ||
      contact.relationshipType === "doctor" ||
      contact.relationshipType === "lawyer"
  );

  const getRelationshipIcon = (relationshipType: string) => {
    if (relationshipType === "child") return <Baby className="h-4 w-4" />;
    return <User className="h-4 w-4" />;
  };

  const getRelationshipColor = (relationshipType: string) => {
    switch (relationshipType) {
      case "child":
        return "bg-green-100 text-green-700";
      case "parent":
      case "guardian":
        return "bg-blue-100 text-blue-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  const ContactItem = ({ contact }: { contact: (typeof contacts)[0] }) => (
    <div className="flex items-center gap-3 p-2 rounded-lg bg-muted/30">
      <Avatar className="h-8 w-8">
        <AvatarFallback className={getRelationshipColor(contact.relationshipType)}>
          {getInitials(contact.name)}
        </AvatarFallback>
      </Avatar>
      <div className="flex-1 min-w-0">
        <P className="font-medium text-sm truncate">{contact.name}</P>
        <div className="flex items-center gap-1 text-xs text-muted-foreground">
          {getRelationshipIcon(contact.relationshipType)}
          <span className="capitalize">{contact.relationshipType.replace("_", " ")}</span>
        </div>
      </div>
      <div className="flex flex-col gap-1">
        {contact.phone && (
          <a href={`tel:${contact.phone}`} className="text-blue-600 hover:text-blue-800">
            <Phone className="h-3 w-3" />
          </a>
        )}
        {contact.email && (
          <a href={`mailto:${contact.email}`} className="text-blue-600 hover:text-blue-800">
            <Mail className="h-3 w-3" />
          </a>
        )}
      </div>
    </div>
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Users className="h-5 w-5 mr-2" />
            {t.title}
          </div>
          <Badge variant="outline" className="text-xs">
            {contacts.length} {t.contacts}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {contacts.length > 0 ? (
          <>
            {/* Children Section */}
            {children.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Baby className="h-4 w-4 text-green-600" />
                  <P className="text-sm font-medium">
                    {t.children} ({children.length})
                  </P>
                </div>
                <div className="space-y-2">
                  {children.map((contact) => (
                    <ContactItem key={contact.id} contact={contact} />
                  ))}
                </div>
              </div>
            )}

            {/* Adults Section */}
            {adults.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-blue-600" />
                  <P className="text-sm font-medium">
                    {t.adults} ({adults.length})
                  </P>
                </div>
                <div className="space-y-2">
                  {adults.map((contact) => (
                    <ContactItem key={contact.id} contact={contact} />
                  ))}
                </div>
              </div>
            )}

            {/* Professionals Section */}
            {professionals.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-purple-600" />
                  <P className="text-sm font-medium">Professionals ({professionals.length})</P>
                </div>
                <div className="space-y-2">
                  {professionals.map((contact) => (
                    <ContactItem key={contact.id} contact={contact} />
                  ))}
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="flex items-center gap-3 p-4 bg-muted/50 rounded-lg">
            <Users className="h-8 w-8 text-muted-foreground" />
            <div>
              <P className="font-medium text-muted-foreground">{t.noContacts}</P>
              <P className="text-xs text-muted-foreground">Add contacts to get started</P>
            </div>
          </div>
        )}

        {/* Action Button */}
        <Button variant="outline" size="sm" className="w-full" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=contacts`}>
            {t.viewAllContacts} ({contacts.length})
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
