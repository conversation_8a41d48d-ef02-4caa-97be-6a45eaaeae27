import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { P } from "@/components/typography";
import { FileText, MapPin, Calendar, Clock, RotateCcw, ArrowRight } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";
import Link from "next/link";

interface ServiceRequirementsProps {
  serviceRequirements: CaseFileDashboardData["serviceRequirements"];
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    serviceName?: string;
    location?: string;
    frequency?: string;
    duration?: string;
    progress?: string;
    viewDetails?: string;
  };
}

/**
 * Service Requirements component for case file dashboard
 * Displays detailed service information including schedule, location, and progress
 */
export function ServiceRequirements({
  serviceRequirements: _serviceRequirements,
  caseFileId,
  lang,
  dictionary,
}: ServiceRequirementsProps) {
  // Default translations
  const defaultDictionary = {
    title: "Service Requirements",
    description: "Service schedule and progress overview",
    serviceName: "Service",
    location: "Location",
    frequency: "Frequency",
    duration: "Duration",
    progress: "Progress",
    viewDetails: "View Details",
  };

  const t = {
    ...defaultDictionary,
    ...dictionary,
  };

  // Mock service data - TODO: Replace with real data from case file
  const mockServiceData = {
    serviceName: "Family Supervision Visit",
    location: "Centre Familial Nord",
    startDate: "2024-01-15",
    endDate: "2024-12-15",
    frequency: "weekly",
    duration: 120, // minutes
    totalSessions: 48,
    completedSessions: 12,
    nextSessionDate: "2024-04-15",
    status: "active" as const,
  };

  // Calculate progress percentage
  const progressPercentage = Math.round(
    (mockServiceData.completedSessions / mockServiceData.totalSessions) * 100
  );

  // Calculate months elapsed and total
  const startDate = new Date(mockServiceData.startDate);
  const endDate = new Date(mockServiceData.endDate);
  const currentDate = new Date();

  const totalMonths = Math.round(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30)
  );
  const elapsedMonths = Math.round(
    (currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24 * 30)
  );

  const getFrequencyIcon = (frequency: string) => {
    switch (frequency) {
      case "weekly":
        return <RotateCcw className="h-4 w-4" />;
      case "monthly":
        return <Calendar className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getFrequencyText = (frequency: string) => {
    switch (frequency) {
      case "weekly":
        return "Weekly";
      case "monthly":
        return "Monthly";
      case "biweekly":
        return "Bi-weekly";
      default:
        return "Custom";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      case "suspended":
        return "bg-orange-100 text-orange-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            {t.title}
          </div>
          <Badge className={getStatusColor(mockServiceData.status)}>
            {mockServiceData.status}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Service Information */}
        <div className="space-y-3">
          <div>
            <P className="font-medium text-sm">{mockServiceData.serviceName}</P>
            <div className="flex items-center gap-1 text-xs text-muted-foreground mt-1">
              <MapPin className="h-3 w-3" />
              {mockServiceData.location}
            </div>
          </div>

          {/* Service Schedule */}
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div>
              <div className="flex items-center gap-1 text-muted-foreground">
                {getFrequencyIcon(mockServiceData.frequency)}
                <span className="text-xs">{t.frequency}</span>
              </div>
              <P className="font-medium">{getFrequencyText(mockServiceData.frequency)}</P>
            </div>
            <div>
              <div className="flex items-center gap-1 text-muted-foreground">
                <Clock className="h-3 w-3" />
                <span className="text-xs">{t.duration}</span>
              </div>
              <P className="font-medium">{mockServiceData.duration} min</P>
            </div>
          </div>

          {/* Progress Section */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-xs text-muted-foreground">{t.progress}</span>
              <span className="text-xs font-medium">{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>{mockServiceData.completedSessions} of {mockServiceData.totalSessions} sessions</span>
              <span>{elapsedMonths} of {totalMonths} months</span>
            </div>
          </div>

          {/* Timeline */}
          <div className="text-xs text-muted-foreground">
            <div className="flex justify-between">
              <span>Start: {new Date(mockServiceData.startDate).toLocaleDateString()}</span>
              <span>End: {new Date(mockServiceData.endDate).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Action Button */}
        <Button variant="outline" size="sm" className="w-full" asChild>
          <Link href={`/${lang}/protected/case-file/${caseFileId}/active?tab=appointments`}>
            {t.viewDetails}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Link>
        </Button>
      </CardContent>
    </Card>
  );
}
