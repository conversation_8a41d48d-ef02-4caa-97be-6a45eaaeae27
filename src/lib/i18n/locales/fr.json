{"common": {"appName": "Si Simple - Plateforme de gestion des visites supervisées", "welcome": "Bienvenue sur la plateforme Si Simple", "loading": "Chargement...", "save": "Enregistrer", "create": "<PERSON><PERSON><PERSON>", "all": "Tous", "language": "<PERSON><PERSON>", "french": "Français", "english": "<PERSON><PERSON><PERSON>", "help": "Aide", "contactUs": "Nous contacter", "error": "<PERSON><PERSON><PERSON>", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "goHome": "Accueil", "pageNotFound": "Page non trouvée", "cancel": "Annuler", "search": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Rechercher...", "clear": "<PERSON><PERSON><PERSON><PERSON>", "by": "par", "actions": "Actions", "saving": "Enregistrement...", "remove": "<PERSON><PERSON><PERSON><PERSON>", "back": "Retour", "goBack": "Retour", "returnToHomepage": "Retour à l'accueil", "errorOccurred": "Une erreur est survenue", "previous": "Précédent", "next": "Suivant", "submit": "So<PERSON><PERSON><PERSON>", "submitting": "Soumission en cours...", "errorMessage": "Une erreur s'est produite. Veuillez réessayer.", "errorId": "ID d'erreur", "profile": "Profil", "account": "<PERSON><PERSON><PERSON>", "signOut": "Déconnexion", "settings": "Paramètres", "notifications": "Notifications", "notSpecified": "Non spécifié", "noAddressProvided": "<PERSON><PERSON>ne adresse fournie", "noEmail": "<PERSON><PERSON><PERSON> co<PERSON>", "noEmailsListed": "<PERSON><PERSON><PERSON> adresse co<PERSON>", "noNumber": "Aucun numéro", "noPhoneNumbers": "Aucun numéro de téléphone listé", "noProfileImage": "Aucune image de profil", "noSpecializationsListed": "Aucune spécialisation listée", "noCertificationsListed": "Aucune certification listée", "noEducationHistory": "Aucun historique d'éducation listé", "noJobTitle": "Aucun titre de poste", "notAssigned": "Non assigné", "noneAssigned": "Aucun <PERSON>", "notLinked": "Non lié", "noPhone": "Pas de numéro de téléphone", "unknown": "Inconnu", "unknownInstitution": "Institution inconnue", "unknownDegree": "<PERSON><PERSON><PERSON><PERSON> inconnu", "unknownField": "Domaine inconnu", "primary": "Principal", "systemInformation": "Informations système", "created": "<PERSON><PERSON><PERSON>", "lastUpdated": "Dernière mise à jour", "notAvailable": "N/D", "unnamed": "Sans nom", "issuer": "<PERSON><PERSON><PERSON>", "obtained": "Obtenu", "expires": "Expire", "present": "Présent", "notFound": {"title": "Page non trouvée", "code": "404", "message": "La page que vous recherchez n'existe pas ou a été déplacée."}, "placeholders": {"enterFirstName": "Entrez le prénom", "enterLastName": "Entrez le nom", "enterFullAddress": "Entrez l'adresse complète", "enterProfileImageUrl": "Entrez l'URL de l'image de profil", "enterEmailAddresses": "Entrez les adresses e-mail (format JSON)", "enterPhoneNumbers": "Entrez les numéros de téléphone (format JSON)", "enterEmployeeId": "Entrez l'ID de l'employé", "selectStatus": "Sélectionnez le statut", "enterJobTitle": "Entrez le titre du poste", "selectDepartment": "Sélectionnez le département", "selectGender": "Sélectionnez le genre", "enterSupervisorId": "Entrez l'ID du superviseur", "enterUserAccountId": "Entrez l'ID du compte utilisateur (optionnel)", "enterSpecializations": "Entrez les spécialisations (séparées par des virgules)", "enterCertifications": "Entrez les certifications (format JSON)", "enterEducation": "Entrez l'historique de formation (format JSON)"}, "gender": {"notSpecified": "Non spécifié", "male": "<PERSON><PERSON>", "female": "<PERSON>mme", "other": "<PERSON><PERSON>", "preferNotToSay": "<PERSON><PERSON><PERSON><PERSON> ne pas préciser"}, "department": {"notSpecified": "Non spécifié", "hr": "<PERSON><PERSON><PERSON><PERSON> humaines", "it": "Technologie de l'information", "finance": "Finance", "operations": "Opérations", "sales": "<PERSON><PERSON><PERSON>", "marketing": "Marketing"}, "edit": "Modifier", "pagination": {"previous": "Précédent", "next": "Suivant", "showing": "Affichage de {count} sur {total} éléments"}, "INSERT": "<PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE": "Supprimé"}, "errors": {"title": "<PERSON><PERSON><PERSON>", "networkError": "<PERSON><PERSON><PERSON> de réseau. Veuillez vérifier votre connexion et réessayer.", "authError": "Erreur d'authentification. Vous devrez peut-être vous reconnecter.", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer plus tard.", "validationError": "Veuillez vérifier le formulaire pour les erreurs.", "invalidEmail": "Format d'email invalide.", "invalidPhone": "Format de numéro de téléphone invalide.", "invalidLanguage": "Sélection de langue invalide.", "requiredField": "Ce champ est obligatoire.", "minLength": "Ce champ doit comporter au moins {min} caractères.", "maxLength": "Ce champ doit comporter au maximum {max} caractères.", "invalidFormat": "Format invalide.", "duplicateEmail": "Cet email est déjà utilisé par un autre compte."}, "home": {"title": "Si Simple 2025", "description": "Une application moderne avec support du mode sombre", "demoText": "Ceci est une démonstration du système de design avec Tailwind CSS v4 et les composants shadcn/ui.", "themeToggleText": "Essayez de basculer entre les modes clair et sombre en utilisant le bouton dans le coin supérieur droit.", "getStarted": "Commencer"}, "navigation": {"dashboard": "Tableau de bord", "user": "Profil", "contacts": "Contacts", "requests": "<PERSON><PERSON><PERSON>", "caseFiles": "Dossiers", "cases": "Dossiers", "scheduling": "<PERSON><PERSON><PERSON>", "notes": "Notes", "reports": "Rapports", "settings": "Paramètres", "admin": "Administration", "organizationAdmin": "Administration Systeme", "userManage": "Gestion des accès", "organizationProfile": "Mon organization", "employeeManage": "Gestion des employés", "employeeAvailability": "Gestion des disponibilités", "documentManagement": "Gestion des documents", "appointments": "<PERSON><PERSON><PERSON>vous", "assignments": "Assignations"}, "auth": {"signin": {"pageTitle": "Connexion", "pageDescription": "Connectez-vous à votre compte", "title": "Connectez-vous à votre compte", "subtitle": "Entrez vos identifiants pour accéder à votre compte", "emailLabel": "<PERSON><PERSON><PERSON>", "passwordLabel": "Mot de passe", "forgotPassword": "Mot de passe oublié?", "signInButton": "Se connecter", "signingIn": "Connexion en cours...", "noAccount": "Vous n'avez pas de compte?", "contactAdmin": "Contactez votre administrateur", "errorMessage": "Une erreur s'est produite lors de la connexion", "unexpectedError": "Une erreur inattendue s'est produite. Veuillez réessayer."}, "signout": {"title": "Déconnexion", "confirmMessage": "Êtes-vous sûr de vouloir vous déconnecter?", "confirmButton": "<PERSON><PERSON>, me déconnecter", "cancelButton": "Annuler"}, "resetPassword": {"pageTitle": "Réinitialisation du mot de passe", "pageDescription": "Réinitialisez le mot de passe de votre compte", "requestResetTitle": "Réinitialisez votre mot de passe", "setNewPasswordTitle": "Définir un nouveau mot de passe", "emailLabel": "<PERSON><PERSON><PERSON>", "passwordLabel": "Nouveau mot de passe", "confirmPasswordLabel": "Confirmer le nouveau mot de passe", "requestButton": "Envoyer le lien de réinitialisation", "resetButton": "Réinitialiser le mot de passe", "backToSignIn": "Retour à la connexion", "requestSent": "Lien de réinitialisation envoy<PERSON> à votre courriel", "passwordMismatch": "Les mots de passe ne correspondent pas", "resetSuccess": "Mot de passe réinitialisé avec succès"}, "errors": {"notFound": "La page d'authentification que vous recherchez n'existe pas."}}, "user": {"profile": "Profil", "profileDescription": "<PERSON><PERSON><PERSON> vos informations de profil et vos préférences.", "personal": "Personnel", "personalDescription": "Mettez à jour vos informations personnelles.", "account": "<PERSON><PERSON><PERSON>", "accountDescription": "<PERSON><PERSON><PERSON> les informations et le mot de passe de votre compte.", "contact": "Contact", "contactDescription": "Mettez à jour vos informations de contact.", "settings": "Paramètres", "settingsDescription": "<PERSON><PERSON><PERSON> les paramètres et préférences de votre compte.", "firstName": "Prénom", "lastName": "Nom", "email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "language": "<PERSON><PERSON>", "notifications": "Notifications", "emailNotifications": "Notifications par courriel", "smsNotifications": "Notifications par SMS", "inAppNotifications": "Notifications dans l'application", "profileUpdated": "Profil mis à jour", "personalInfoUpdated": "Informations personnelles mises à jour avec succès.", "contactInfoUpdated": "Informations de contact mises à jour avec succès.", "settingsUpdated": "Paramètres mis à jour avec succès.", "name": "Nom", "role": "R<PERSON><PERSON>", "selectRole": "Sélectionner un rôle", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "systemInfo": "Informations système", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "roles": {"director": "Directeur", "coordinator": "Coordinateur", "socialWorker": "Travailleur social", "systemAdmin": "Administrateur système"}, "management": {"title": "Gestion des utilisateurs", "description": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "userList": "Liste des utilisateurs", "userListDescription": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "createUser": "C<PERSON>er un utilisateur", "createUserDescription": "Ajouter un nouvel utilisateur à votre organisation", "editUser": "Modifier l'utilisateur", "editUserDescription": "Mettre à jour les informations de l'utilisateur", "userDetails": "Détails de l'utilisateur", "userCreated": "Utilisateur c<PERSON>é avec succès", "userUpdated": "Utilisateur mis à jour avec succès", "emailCannotBeChanged": "L'adresse courriel ne peut pas être modifiée", "noUsersFound": "Aucun utilisateur trouvé", "filterByRole": "Filtrer par rôle", "userNotFound": "Utilisateur non trouvé", "userNotFoundDescription": "L'utilisateur que vous recherchez n'existe pas ou vous n'avez pas la permission de le voir."}}, "organization": {"list": "Organisations", "createNew": "Créer une nouvelle organisation", "noOrganizations": "Aucune organisation trouvée. Créez votre première organisation pour commencer.", "details": "Détails de l'organisation", "backToList": "Retour à la liste", "edit": "Modifier", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mise à jour le", "assignDirector": "Assigner un directeur", "profileManagement": "Mon organisation", "profileManagementDescription": "<PERSON><PERSON><PERSON> le profil, les emplacements, les services et les utilisateurs de votre organisation", "generalInfo": "Informations générales", "generalInfoDescription": "Informations de base sur votre organisation", "locations": "Emplacements", "locationsDescription": "<PERSON><PERSON>rez les emplacements et les salles de votre organisation", "services": "Services", "servicesDescription": "Configurez les services offerts par votre organisation", "users": "Utilisateurs", "usersDescription": "<PERSON><PERSON>rez les utilisateurs de votre organisation", "profileUpdated": "Profil de l'organisation mis à jour avec succès", "addLocation": "Ajouter un emplacement", "editLocation": "Modifier l'emplacement", "deleteLocation": "Supprimer l'emplacement", "noLocations": "Aucun emplacement trouvé. Ajoutez votre premier emplacement.", "locationCreated": "Emplacement créé avec succès", "locationUpdated": "Emplacement mis à jour avec succès", "deleteLocationConfirm": "Êtes-vous sûr de vouloir supprimer cet emplacement?", "addService": "Ajouter un service", "editService": "Modifier le service", "deleteService": "Supprimer le service", "noServices": "Aucun service trouvé. Ajoutez votre premier service.", "serviceCreated": "Service créé avec succès", "serviceUpdated": "Service mis à jour avec succès", "deleteServiceConfirm": "Êtes-vous sûr de vouloir supprimer ce service?", "businessHours": "Heures d'ouverture", "businessHoursDescription": "Configurez les heures d'ouverture de votre organisation", "addBusinessHours": "Ajouter des heures d'ouverture", "editBusinessHours": "Modifier les heures d'ouverture", "deleteBusinessHours": "Supprimer les heures d'ouverture", "noBusinessHours": "Aucune heure d'ouverture trouvée. Ajoutez vos premières heures d'ouverture.", "businessHoursCreated": "Heures d'ouverture créées avec succès", "businessHoursUpdated": "Heures d'ouverture mises à jour avec succès", "deleteBusinessHoursConfirm": "Êtes-vous sûr de vouloir supprimer ces heures d'ouverture?", "dayOfWeek": "<PERSON><PERSON> <PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "isClosed": "<PERSON><PERSON><PERSON>", "location": "Emplacement", "allLocations": "Tous les emplacements", "description": "Description", "price": "Prix", "duration": "<PERSON><PERSON><PERSON>", "minutes": "minutes", "status": "Statut", "active": "Actif", "inactive": "Inactif", "cancel": "Annuler", "delete": "<PERSON><PERSON><PERSON><PERSON>", "rooms": "<PERSON><PERSON>", "roomsDescription": "G<PERSON>rer les salles pour les emplacements de votre organisation", "addRoom": "Ajouter une salle", "editRoom": "Modifier la salle", "deleteRoom": "Supp<PERSON>er la salle", "noRooms": "Aucune salle trouvée. Ajoutez votre première salle.", "roomCreated": "Salle créée avec succès", "roomUpdated": "Salle mise à jour avec succès", "deleteRoomConfirm": "Êtes-vous sûr de vouloir supprimer cette salle?", "addRoomDescription": "Ajouter une nouvelle salle à l'un de vos emplacements", "editRoomDescription": "Modifier les détails et les caractéristiques de la salle", "capacity": "Capacité", "features": "Caractéristiques", "addCustomFeature": "Ajouter une caractéristique personnalisée", "featureName": "Nom de la caractéristique", "featureDescription": "Description de la caractéristique", "add": "Ajouter", "filterByLocation": "Filtrer par emplacement", "form": {"createOrganization": "Créer une organisation", "editOrganization": "Modifier l'organisation", "organizationDetails": "Détails de l'organisation", "contactInfo": "Informations de contact", "name": "Nom de l'organisation", "address": "<PERSON><PERSON><PERSON>", "phone": "Numéro de téléphone", "fax": "Numéro de fax", "email": "<PERSON><PERSON><PERSON>", "supportEmail": "<PERSON><PERSON><PERSON> co<PERSON><PERSON> de <PERSON>", "website": "Site web", "status": "Statut", "active": "Active", "inactive": "Inactive", "suspended": "Su<PERSON>end<PERSON>", "create": "Créer l'organisation", "update": "Mettre à jour l'organisation", "cancel": "Annuler", "required": "Requis", "invalidEmail": "<PERSON><PERSON><PERSON> co<PERSON> invalide", "invalidPhone": "Numéro de téléphone invalide", "firstName": "Prénom", "lastName": "Nom de famille", "assignDirector": "Assigner un directeur", "assignDirectorDescription": "Assigner un directeur à cette organisation", "directorAssigned": "Directeur assigné avec succès!", "assigning": "Assignation en cours..."}, "director": {"assignDirector": "Assigner un directeur", "directorDetails": "Entrez les détails du directeur de l'organisation", "firstName": "Prénom", "lastName": "Nom", "email": "<PERSON><PERSON><PERSON>", "phone": "Numéro de téléphone", "assign": "Assigner le directeur", "cancel": "Annuler"}}, "documents": {"title": "Pièces jointes de documents", "description": "Gérer et organiser les pièces jointes de documents pour toutes les entités", "uploadDocuments": "Télécharger des documents", "searchPlaceholder": "Rechercher des documents...", "allEntityTypes": "Tous les types d'entités", "allDocumentTypes": "Tous les types de documents", "entityTypes": {"caseFile": "Dossiers", "contact": "Contacts", "appointment": "<PERSON><PERSON><PERSON>vous", "request": "<PERSON><PERSON><PERSON>"}, "documentTypes": {"wordDocument": "Document Word", "imageJpg": "Image (JPG)", "imagePng": "Image (PNG)", "textFile": "<PERSON><PERSON>er texte"}, "sortBy": {"uploadDate": "Trier par date de téléchargement", "name": "Trier par nom", "size": "Trier par taille", "type": "Trier par type"}, "filters": "Filtres", "noDocumentsFound": "Aucun document trouvé", "documentsFound": "{count} documents trouvés", "errorLoadingAttachments": "Erreur lors du chargement des pièces jointes", "checkConfiguration": "Veuillez vérifier la configuration et réessayer.", "upload": {"title": "Télécharger des documents", "description": "Description", "selectFiles": "Sélectionner des fichiers", "dragAndDrop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> les fichiers ici, ou cliquez pour sélectionner", "category": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Étiquettes", "attachedTo": "Attach<PERSON> à", "entityType": "Type d'entité", "entityId": "ID d'entité", "uploadSuccess": "Téléchargement réussi", "uploadError": "Échec du téléchargement"}, "detail": {"title": "Détails du document", "description": "Description", "back": "Retour", "documentPreview": "Aperçu du document", "previewOf": "<PERSON><PERSON><PERSON><PERSON> de {name}", "previewNotAvailable": "Aperçu non disponible", "previewNotImplemented": "L'aperçu pour les fichiers {type} n'est pas encore implémenté", "download": "Télécharger", "openInNewTab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "documentInformation": "Informations du document", "type": "Type", "size": "<PERSON><PERSON>", "status": "Statut", "source": "Source", "manualUpload": "Téléchargement manuel", "generated": "<PERSON><PERSON><PERSON><PERSON>", "attachedTo": "Attach<PERSON> à", "entityType": "Type d'entité", "entityId": "ID d'entité", "viewEntity": "Voir l'entité", "viewRelated": "Ouvrir relation ➔", "metadata": "Métadonnées", "category": "<PERSON><PERSON><PERSON><PERSON>", "tags": "Étiquettes", "uploadInformation": "Informations de téléchargement", "uploaded": "Télécharg<PERSON>", "uploadedBy": "Téléchargé par", "actions": "Actions", "editMetadata": "Modifier les métadonnées", "delete": "<PERSON><PERSON><PERSON><PERSON>", "deleting": "Suppression...", "deleteConfirmation": "Êtes-vous sûr de vouloir supprimer ce document ? Cette action ne peut pas être annulée.", "timeline": "Chronologie", "documentInfo": "Informations", "language": "Language", "fileName": "Nom", "fileSize": "<PERSON><PERSON>"}}, "appointments": {"title": "<PERSON><PERSON><PERSON>vous", "description": "G<PERSON>rer et voir tous les rendez-vous", "newAppointment": "Nouveau rendez-vous", "createAppointment": "<PERSON><PERSON>er un nouveau rendez-vous", "editAppointment": "Modifier le rendez-vous", "appointmentDetails": "<PERSON><PERSON><PERSON> du rendez-vous", "appointmentInformation": "Informations du rendez-vous", "scheduleOverview": "Aperçu de l'horaire", "calendarView": "<PERSON><PERSON> calendrier", "listView": "<PERSON><PERSON> liste", "backToAppointments": "Retour aux rendez-vous", "backToList": "Retour à la liste", "backToDetails": "Retour aux détails", "viewFullDetails": "Voir tous les détails", "close": "<PERSON><PERSON><PERSON>", "apply": "Appliquer", "filterAppointments": "Filtrer les rendez-vous", "searchAppointments": "Rechercher des rendez-vous...", "allStatuses": "Tous les statuts", "activeFilters": "Filtres actifs:", "noAppointmentsFound": "Aucun rendez-vous trouvé", "loadingAppointments": "Chargement des rendez-vous...", "loadingCalendar": "Chargement du calendrier...", "loadingForm": "Chargement du formulaire...", "loadingAppointmentDetails": "Chargement des détails du rendez-vous...", "errorCreatingAppointment": "<PERSON><PERSON>ur lors de la création du rendez-vous", "appointmentNotFound": "<PERSON><PERSON>-vous introuvable", "appointmentNotFoundDescription": "Le rendez-vous que vous recherchez n'existe pas.", "appointmentEditNotFoundDescription": "Le rendez-vous que vous essayez de modifier n'existe pas ou vous n'avez pas la permission de le modifier.", "errorLoadingForm": "Une erreur s'est produite lors du chargement du formulaire de rendez-vous.", "scheduleAppointment": "Planifier un nouveau rendez-vous", "updateAppointmentInformation": "Mettre à jour les informations du rendez-vous", "selfContained": "Autonome", "noCaseFileLink": "Aucun lien vers le dossier", "noServiceLink": "Aucun lien vers le service", "integrationStatus": "Statut d'intégration", "integrationDescription": "Ce rendez-vous fonctionne en mode autonome et peut être intégré avec d'autres systèmes plus tard.", "basicInformation": "Informations de base", "schedule": "<PERSON><PERSON><PERSON>", "timeline": "Chronologie", "systemInformation": "Informations système", "appointmentId": "ID du rendez-vous", "organizationId": "ID de l'organisation", "form": {"title": "Titre", "titlePlaceholder": "Entrez le titre du rendez-vous", "description": "Description", "descriptionPlaceholder": "Entrez la description du rendez-vous (optionnel)", "date": "Date", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "status": "Statut", "selectStatus": "Sélectionner le statut", "createAppointment": "<PERSON><PERSON><PERSON> le rendez-vous", "updateAppointment": "Mettre à jour le rendez-vous", "cancel": "Annuler", "creating": "Création...", "updating": "Mise à jour...", "validation": {"titleRequired": "Le titre est requis", "titleTooShort": "Le titre est trop court", "titleTooLong": "Le titre est trop long", "descriptionTooLong": "La description est trop longue", "dateRequired": "La date est requise", "startTimeRequired": "L'heure de début est requise", "endTimeRequired": "L'heure de fin est requise", "endTimeAfterStart": "L'heure de fin doit être après l'heure de début", "pastDate": "Impossible de créer des rendez-vous dans le passé"}}, "status": {"planned": "Planifié", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "inProgress": "En cours", "completed": "<PERSON><PERSON><PERSON><PERSON>", "missed": "<PERSON><PERSON><PERSON>", "postponed": "Reporté", "cancelled": "<PERSON><PERSON><PERSON>"}, "statusDescriptions": {"planned": "Le rendez-vous est planifié mais pas confirmé", "confirmed": "Le rendez-vous est confirmé par toutes les parties", "inProgress": "Le rendez-vous est en cours", "completed": "Le rendez-vous a été terminé avec succès", "missed": "Le rendez-vous a été manqué par le participant", "postponed": "Le rendez-vous a été reporté à une date ultérieure", "cancelled": "Le rendez-vous a été annulé"}, "actions": {"view": "Voir le rendez-vous", "edit": "Modifier le rendez-vous", "confirm": "Confirmer", "complete": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "postpone": "Reporter", "reschedule": "Reprogrammer"}, "messages": {"created": "<PERSON><PERSON><PERSON>vous c<PERSON>é avec succès", "updated": "<PERSON><PERSON><PERSON>vous mis à jour avec succès", "deleted": "Rendez-vous supprimé avec succès", "confirmed": "<PERSON><PERSON><PERSON>vous <PERSON><PERSON> avec succès", "completed": "<PERSON><PERSON><PERSON>vous terminé avec succès", "cancelled": "Rendez-vous annulé avec succès", "postponed": "Rendez-vous reporté avec succès", "conflictDetected": "Conflit d'horaire détecté", "conflictMessage": "Il y a déjà un rendez-vous planifié pendant cette période"}}, "caseFileOpening": {"wizard": {"welcome": {"title": "Bienvenue à l'ouverture du dossier", "subtitle": "Préparons votre dossier pour une gestion active", "description": "Ce processus guidé vous aidera à configurer les flux de documents, la gestion des contacts et les exigences de signature pour ce dossier.", "features": {"documents": {"title": "Gestion des documents", "description": "Organisez et suivez tous les documents liés au dossier avec signatures numériques"}, "contacts": {"title": "Flux de contacts", "description": "<PERSON><PERSON><PERSON> les contacts familiaux et leurs exigences documentaires"}, "tracking": {"title": "Suivi des progrès", "description": "Surveillez l'état d'achèvement et les actions requises en temps réel"}}, "getStarted": "Commencer"}, "contacts": {"title": "<PERSON><PERSON> familia<PERSON>", "subtitle": "Examinez les contacts associés à ce dossier", "description": "<PERSON><PERSON> contacts auront accès aux documents du dossier et devront peut-être compléter les exigences de signature.", "noContacts": "Aucun contact trouvé", "noContactsDescription": "Ajoutez des contacts pour commencer avec les flux de documents", "continueToDocuments": "Continuer vers les documents"}, "documents": {"title": "Flux de documents prêt", "subtitle": "Votre dossier est maintenant configuré pour la gestion des documents", "successMessage": "Parfait ! Vous êtes prêt.", "successDescription": "Sélectionnez un contact et complétez les actions documentaires requises."}, "breadcrumb": {"welcome": "Bienvenue", "contacts": "Contacts", "documents": "Documents", "complete": "<PERSON><PERSON><PERSON><PERSON>"}}, "contacts": {"title": "Contacts", "description": "Cliquez sur un contact pour voir ses documents", "noContacts": "Aucun contact trouvé", "noContactsDescription": "Ajoutez des contacts pour commencer"}, "documents": {"title": "Documents pour {contactName}", "noDocuments": "Aucun document trouvé", "noDocumentsDescription": "Aucun document n'a encore été assigné à ce contact.", "progressSummary": "Résumé des progrès", "progressDescription": "{completed} sur {total} documents nécessitant une signature complétés", "complete": "<PERSON><PERSON><PERSON><PERSON>", "status": {"completed": "<PERSON><PERSON><PERSON><PERSON>", "needsSignature": "Signature requise", "needsReview": "Révision requise", "rejected": "<PERSON><PERSON><PERSON>", "expired": "Expiré", "review": "Révision"}, "actions": {"viewDocument": "Voir le document", "signDocument": "Signer le document"}, "badges": {"signatureRequired": "Signature requise", "required": "Requis"}, "signedOn": "<PERSON><PERSON> {date}"}, "navigation": {"backToContacts": "Retour aux contacts", "viewingDocumentsFor": "Consultation des documents pour"}, "caseFile": {"opening": "Ouverture", "service": "Service", "reference": "Référence", "assignedTo": "<PERSON><PERSON><PERSON>", "requestDate": "Date de demande"}, "emptyDocumentState": {"title": "Flux de révision et signature de documents", "description": "Ce flux de travail est destiné aux révisions de documents, signatures et accusés de réception.", "instruction": "Veuillez cliquer sur un contact de la liste pour voir ses documents et effectuer les actions requises.", "features": {"viewDocuments": "Voir les documents", "digitalSignatures": "Signatures numériques", "acknowledgments": "Accusés de réception", "progressTracking": "Suivi des progrès"}}}, "template-single-crud": {"feature-1": {"title": "Fonctionnalité 1", "description": "Description de la fonctionnalité 1", "createTitle": "<PERSON><PERSON>er un nouvel élément", "createDescription": "Entrez les détails du nouvel élément", "editTitle": "Modifier l'élément", "editDescription": "Mettre à jour les détails de cet élément", "viewTitle": "Détails de l'élément", "removeTitle": "Supprimer l'élément", "removeDescription": "Êtes-vous sûr de vouloir supprimer cet élément?", "removeConfirm": "<PERSON><PERSON>, supprimer", "removeCancel": "Non, annuler", "backToList": "Retour à la liste", "itemCreated": "Élément créé avec succès", "itemUpdated": "Élément mis à jour avec succès", "itemRemoved": "Élément supprimé avec succès", "noItems": "Aucun élément trouvé. Créez votre premier élément pour commencer.", "fields": {"name": "Nom", "description": "Description", "status": "Statut", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le"}, "status": {"active": "Actif", "inactive": "Inactif", "draft": "Brouillon"}, "actions": {"create": "<PERSON><PERSON><PERSON> un élément", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>"}}}, "automation": {"dashboard": {"title": "Tableau de bord d'automatisation", "employeeCreation": {"title": "Création d'employé", "description": "<PERSON><PERSON>er un nouvel employé", "content": "Utilisez cet assistant pour créer un nouvel employé avec toutes les informations nécessaires.", "startButton": "<PERSON><PERSON><PERSON><PERSON> l'assistant"}, "requestCreation": {"title": "Création de demande", "description": "Créer une nouvelle demande de service", "content": "Utilisez cet assistant pour créer une nouvelle demande de service avec tous les détails nécessaires.", "startButton": "<PERSON><PERSON><PERSON><PERSON> l'assistant"}}, "execution": {"title": "Statut d'exécution du flux de travail", "description": "Suivez l'état de votre exécution de flux de travail"}, "request-wizard": {"title": "<PERSON><PERSON><PERSON> une demande", "description": "Complétez les étapes suivantes pour créer une nouvelle demande", "startButton": "Commencer la création de la demande", "steps": {"basic_info": "Informations de base", "related_contacts": "Contacts associés", "service_requirements": "Exigences de service", "family_availability": "Disponibilité familiale", "review": "Révision"}, "basic_info": {"service": "Service", "selectService": "Sélectionner un service", "location": "Emplacement", "selectLocation": "Sélectionner un emplacement"}, "service_requirements": {"startDate": "Date de début", "selectStartDate": "Sélectionner une date de début", "endDate": "Date de fin", "selectEndDate": "Sélectionner une date de fin", "frequency": "<PERSON><PERSON><PERSON>", "selectFrequency": "Sélectionner une fréquence", "periodicity": "Périodicité", "selectPeriodicity": "Sélectionner une périodicité", "frequencyCount": "<PERSON><PERSON><PERSON>", "enterFrequencyCount": "Entrer le nombre de fois par période", "timesPerPeriod": "fois par période", "daily": "Quotidien", "weekly": "Hebdomadaire", "biweekly": "Bihebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "custom": "<PERSON><PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON> (minutes)", "enterDuration": "Entrer la durée en minutes", "preferredDays": "Jours préfé<PERSON>s", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>", "preferredTime": "Moment préféré de la journée", "selectPreferredTime": "Sélectionner le moment préféré", "morning": "<PERSON><PERSON> (8h-12h)", "afternoon": "Après-midi (12h-17h)", "evening": "Soir (17h-21h)", "flexible": "Flexible", "notes": "Notes supplémentaires", "enterNotes": "Entrez toutes exigences ou notes supplémentaires"}, "related_contacts": {"title": "Ajouter des contacts associés", "description": "Sélectionnez les contacts associés à cette demande. <PERSON><PERSON> pouvez ajouter plusieurs contacts avec différentes relations.", "addContact": "Ajouter un contact", "noContacts": "Aucun contact ajouté pour l'instant. Cliquez sur le bouton ci-dessus pour ajouter des contacts.", "selectedContacts": "Contacts sélectionnés", "addNewContact": "Ajouter un nouveau contact", "contact": "Contact", "searchContact": "Rechercher un contact...", "noContactsFound": "Aucun contact trouvé", "minCharactersMessage": "Tapez au moins 2 caractères pour rechercher", "relationship": "Type de relation", "selectRelationship": "Sélectionner un type de relation", "client": "Client", "familyMember": "Membre de la famille", "guardian": "<PERSON><PERSON><PERSON>", "caregiver": "Soignant", "professional": "Professionnel", "other": "<PERSON><PERSON>", "actions": "Actions", "remove": "<PERSON><PERSON><PERSON><PERSON>"}, "review": {"title": "Révision", "description": "Veuillez vérifier les informations ci-dessous avant de soumettre votre demande.", "basicInfo": "Informations de base", "complete": "Complet", "incomplete": "Incomplet", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "relatedContacts": "Contacts associés", "submit": "Soumettre la demande", "notProvided": "Non fourni", "notSpecified": "Non spécifié", "noRelatedContacts": "Aucun contact associé a<PERSON>"}, "family_availability": {"title": "Disponibilité familiale", "generalAvailability": "Disponibilité générale", "weekdaysOnly": "Jours de semaine seulement", "weekendsOnly": "Fins de semaine seulement", "bothWeekdaysWeekends": "Jours de semaine et fins de semaine", "flexible": "Flexible", "timeFlexibility": "Flexibilité horaire", "selectTimeFlexibility": "Sélectionner la flexibilité horaire", "veryFlexible": "Très flexible (peut s'adapter à n'importe quel horaire)", "somewhatFlexible": "As<PERSON>z flexible (préfère certains horaires mais peut s'adapter)", "limitedFlexibility": "Flexibilité limitée (nécessite des horaires spécifiques)", "advanceNotice": "<PERSON><PERSON><PERSON><PERSON> requis", "selectAdvanceNotice": "Sélectionner le préavis requis", "hours24": "24 heures", "days2": "2-3 jours", "week1": "1 semaine", "weeks2": "2 semaines", "month1": "1 mois ou plus", "notes": "Notes supplémentaires", "enterNotes": "Entrez toutes notes supplémentaires concernant la disponibilité"}}}, "request": {"title": "<PERSON><PERSON><PERSON>", "description": "Gérer vos demandes", "createNew": "<PERSON><PERSON><PERSON> une nouvelle demande", "noRequests": "Aucune demande trouvée. Créez votre première demande pour commencer.", "details": "<PERSON><PERSON><PERSON> de la demande", "viewTitle": "Voir la demande", "viewDescription": "Voir et gérer les informations de la demande", "backToList": "Retour à la liste", "actions": "Actions", "historyTitle": "Historique de la demande", "historyDescription": "Chronologie des modifications apportées à cette demande", "advancedFilters": "Filtres avancés", "searchRequests": "Rechercher des demandes...", "status": {"draft": "Brouillon", "processing": "En Traitement", "waitlist": "Liste d'attente", "completed": "Complété", "closed": "<PERSON><PERSON><PERSON>"}, "statusDescriptions": {"draft": "État initial, la demande est en cours de préparation et n'a pas encore été soumise", "processing": "La demande a été soumise et est en cours de traitement selon les règles d'affaires", "waitlist": "Le service ne peut pas être fourni immédiatement en raison de contraintes de ressources", "completed": "Le service peut être fourni, le dossier a été initié", "closed": "La demande a été fermée manuellement et n'est plus active"}, "priority": {"low": "Faible", "medium": "<PERSON><PERSON><PERSON>", "high": "Élevée"}, "serviceType": {"supervised_visitation": "Visite supervisée", "exchange": "Échange", "transportation": "Transport", "consultation": "Consultation", "other": "<PERSON><PERSON>"}, "fields": {"title": "Titre", "description": "Description", "status": "Statut", "priority": "Priorité", "serviceType": "Type de service", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le", "requester": "De<PERSON>eur", "assignee": "<PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "location": "Emplacement", "service": "Service", "frequency": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "preferredDays": "Jours préfé<PERSON>s", "preferredTime": "<PERSON><PERSON> préfé<PERSON>", "referenceNumber": "Numéro de référence", "contacts": "Contacts", "relatedContacts": "Contacts liés", "actions": "Actions"}, "notSpecified": "Non spécifié", "minutes": "minutes", "serviceTimeline": "Chronologie du service", "requestCreated": "<PERSON><PERSON><PERSON>", "currentStatus": "Statut actuel", "serviceStart": "Début du service", "serviceEnd": "Fin du service", "noContactsAvailable": "Aucun contact disponible", "noLocationSpecified": "Aucun emplacement spécifié", "uniqueIdentifier": "Identifiant unique", "weekdaysOnly": "Jours de semaine seulement", "weekendsOnly": "Fins de semaine seulement", "weekdaysAndWeekends": "Jours de semaine et fins de semaine", "flexible": "Flexible", "periodicity": "Périodicité", "frequencyCount": "<PERSON><PERSON><PERSON>", "timesPerPeriod": "fois par période", "periodicities": {"daily": "Quotidien", "weekly": "Hebdomadaire", "monthly": "<PERSON><PERSON><PERSON>"}, "wizard": {"wizardTitle": "<PERSON><PERSON><PERSON> une nouvelle demande", "wizardDescription": "Complétez les étapes suivantes pour créer une nouvelle demande", "steps": {"basicInfo": "Informations de base", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "review": "Révision"}, "stepTitles": {"basicInfo": "Informations de base", "serviceRequirements": "Exigences de service", "familyAvailability": "Disponibilité familiale", "review": "Révision et soumission"}, "buttons": {"nextServiceRequirements": "Suivant: Exigences de service", "nextFamilyAvailability": "Suivant: Disponibilité familiale", "nextReview": "Suivant: Révision", "submit": "Soumettre la demande", "previous": "Précédent", "startOver": "Recommencer"}, "success": {"title": "<PERSON><PERSON><PERSON> c<PERSON> avec succès", "message": "Votre demande a été créée et est maintenant en statut brouillon.", "viewRequest": "Voir la demande", "createAnother": "<PERSON><PERSON><PERSON> une autre demande"}}, "serviceRequirements": {"title": "Exigences de service", "description": "Spécifiez les exigences pour cette demande de service", "frequency": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON> (minutes)", "specialRequirements": "Exigences spéciales", "preferredDays": "Jours préfé<PERSON>s", "preferredTime": "Moment de la journée préféré", "notes": "Notes supplémentaires", "frequencies": {"weekly": "Hebdomadaire", "biweekly": "Bihebdomadaire", "monthly": "<PERSON><PERSON><PERSON>", "oneTime": "Une seule fois"}, "timeOfDay": {"morning": "<PERSON>in", "afternoon": "Après-midi", "evening": "Soir", "noPreference": "Pas de préférence"}, "days": {"monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>", "sunday": "<PERSON><PERSON><PERSON>"}}, "familyAvailability": {"title": "Disponibilité familiale", "description": "Fournir des informations sur la disponibilité familiale", "generalAvailability": "Disponibilité générale", "specificDates": "Dates spécifiques", "notes": "Notes supplémentaires", "addDate": "Ajouter une date", "removeDate": "<PERSON><PERSON><PERSON><PERSON>", "date": "Date", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "selectContact": "Sélectionnez un contact pour définir sa disponibilité", "changesAutoSaved": "Modifications enregistrées automatiquement", "clickToMark": "Cliquez sur les créneaux horaires pour les marquer comme disponibles", "timeSlots": "créneaux horaires", "noContactsAdded": "Aucun contact ajouté pour l'instant.", "addContacts": "Ajouter des contacts", "selectToSet": "Sélectionnez un contact pour définir sa disponibilité", "weeklyAvailability": "Disponibilité hebdomadaire", "morning": "<PERSON>in", "noon": "Midi", "afternoon": "Après-midi", "evening": "Soir", "available": "Disponible", "noAvailabilitySet": "Aucune disponibilité définie", "availabilitySetFor": "Disponibilité définie pour", "contacts": "contacts", "seeDetails": "Voir les détails dans la grille de disponibilité", "noAvailabilityInfo": "Aucune information de disponibilité fournie"}}, "employee": {"management": {"title": "Gestion des employés", "description": "<PERSON><PERSON>rer les profils et la disponibilité des employés", "createTitle": "<PERSON><PERSON>er un nouvel employé", "createDescription": "Entrez les détails du nouvel employé", "editTitle": "Modifier l'employé", "editDescription": "Mettre à jour les détails de cet employé", "viewTitle": "<PERSON>é<PERSON> de l'employé", "removeTitle": "Supprimer l'employé", "removeDescription": "Êtes-vous sûr de vouloir supprimer cet employé?", "removeConfirm": "<PERSON><PERSON>, supprimer", "removeCancel": "Non, annuler", "removeWarning": "Cette action ne peut pas être annulée. L'employé sera marqué comme licencié.", "backToList": "Retour à la liste", "itemCreated": "Emp<PERSON><PERSON> créé avec succès", "itemUpdated": "Employé mis à jour avec succès", "itemRemoved": "Employé supprimé avec succès", "noItems": "Aucun employé trouvé. Créez votre premier employé pour commencer.", "personalInformation": "Informations personnelles", "tabs": {"contact": "Informations de contact", "employment": "<PERSON><PERSON><PERSON> d'emploi", "professional": "Informations professionnelles", "personal": "Informations personnelles"}, "fields": {"name": "Nom", "description": "Description", "status": "Statut", "firstName": "Prénom", "lastName": "Nom", "fullName": "Nom complet", "profileImage": "Image de profil", "dateOfBirth": "Date de naissance", "gender": "Genre", "address": "<PERSON><PERSON><PERSON>", "employeeId": "ID d'employé", "hireDate": "Date d'embauche", "terminationDate": "Date de fin d'emploi", "employmentStatus": "Statut d'emploi", "jobTitle": "Titre du poste", "department": "Département", "supervisor": "Superviseur", "specializations": "Spécialisations", "certifications": "Certifications", "education": "Éducation", "emails": "<PERSON><PERSON> courriel", "phones": "Numéros de téléphone", "userAccount": "Compte utilisateur", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "Mis à jour le"}, "status": {"active": "Actif", "inactive": "Inactif", "draft": "Brouillon"}, "employmentStatus": {"active": "Actif", "inactive": "Inactif", "terminated": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"create": "<PERSON><PERSON><PERSON> un employé", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>", "activate": "Activer", "deactivate": "Désactiver", "linkUserAccount": "Lier un compte utilisateur"}, "confirmDeactivate": "Confirmer la désactivation", "deactivateDescription": "Êtes-vous sûr de vouloir désactiver cet employé? Il n'aura plus accès au système.", "activating": "Activation en cours...", "deactivating": "Désactivation en cours..."}, "wizard": {"title": "Assistant de création d'employé", "description": "<PERSON><PERSON><PERSON> un nouvel employé en utilisant cet assistant étape par étape", "createTitle": "<PERSON><PERSON>er un nouvel employé", "createDescription": "Cet assistant vous guidera à travers le processus de création d'un nouvel employé. Vous pouvez sauvegarder votre progression à tout moment et revenir plus tard pour la compléter.", "startButton": "Commencer la création d'employé", "steps": {"basicInfo": "Informations de base", "contactInfo": "Coordonnées", "employmentDetails": "<PERSON><PERSON><PERSON> d'emploi", "review": "Révision"}, "stepTitles": {"basicInfo": "Informations de base", "contactInfo": "Coordonnées", "employmentDetails": "<PERSON><PERSON><PERSON> d'emploi", "review": "Révision des informations de l'employé"}, "wizardTitle": "<PERSON><PERSON>er un nouvel employé", "wizardDescription": "Complétez les étapes suivantes pour créer un nouvel employé", "fields": {"firstName": "Prénom", "lastName": "Nom", "jobTitle": "Titre du poste", "department": "Département", "address": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "employmentStatus": "Statut d'emploi", "hireDate": "Date d'embauche", "employeeId": "ID d'employé", "supervisorId": "ID du superviseur", "role": "<PERSON><PERSON><PERSON> utilisateur"}, "placeholders": {"enterFirstName": "Entrez le prénom", "enterLastName": "Entrez le nom", "enterJobTitle": "Entrez le titre du poste", "selectDepartment": "Sélectionnez le département", "enterAddress": "Entrez l'adresse", "enterEmail": "Entrez l'adresse courriel", "enterPhone": "Entrez le numéro de téléphone", "selectEmploymentStatus": "Sélectionnez le statut d'emploi", "enterHireDate": "Entrez la date d'embauche", "enterEmployeeId": "Entrez l'ID d'employé", "enterSupervisorId": "Entrez l'ID du superviseur", "selectRole": "Sélectionnez le rôle"}, "buttons": {"previous": "Précédent", "next": "Suivant", "save": "Enregistrer", "submit": "So<PERSON><PERSON><PERSON>", "previousBasicInfo": "Précédent: Informations de base", "previousContactInfo": "Précédent: Coordonnées", "previousEmploymentDetails": "Précédent: <PERSON><PERSON><PERSON>'emploi", "nextContactInfo": "Suivant: Coordonnées", "nextEmploymentDetails": "Suivant: <PERSON><PERSON><PERSON> d'emploi", "nextReview": "Suivant: Révision", "createEmployee": "<PERSON><PERSON>er l'employé"}, "reviewDescription": "Veuillez vérifier les informations ci-dessous avant de soumettre. Vous pouvez revenir à n'importe quelle étape pour apporter des modifications.", "reviewTable": {"field": "<PERSON><PERSON>", "value": "<PERSON><PERSON>"}, "success": {"title": "Employé créé avec succès!", "description": "L'employé a été créé avec succès. Vous pouvez maintenant voir les détails de l'employé ou créer un autre employé.", "executionId": "ID d'exécution:", "status": "Statut:", "createdAt": "<PERSON><PERSON><PERSON>:", "completed": "Complété", "viewEmployee": "Voir l'employé", "createAnother": "<PERSON><PERSON>er un autre employé"}, "loading": "Chargement...", "roles": {"director": "Directeur", "coordinator": "Coordinateur", "socialWorker": "Travailleur social"}}, "availability": {"title": "Gestion des disponibilités", "description": "Gérer les disponibilités des employés, les demandes de congé et les exceptions", "listTitle": "Disponibilités des employés", "listDescription": "Consulter et gérer les disponibilités des employés", "noItems": "Aucun enregistrement de disponibilité trouvé", "createButton": "Ajouter une disponibilité", "viewTitle": "Détails de disponibilité", "viewDescription": "Consulter les détails de disponibilité d'un employé", "editButton": "Modifier la disponibilité", "backToList": "Retour à la liste", "editTitle": "Modifier la disponibilité", "editDescription": "Mettre à jour la disponibilité d'un employé", "saveButton": "Enregistrer les modifications", "cancelButton": "Annuler", "itemUpdated": "Disponibilité mise à jour avec succès", "createTitle": "Ajouter une disponibilité", "createDescription": "<PERSON><PERSON>er une nouvelle disponibilité pour un employé", "itemCreated": "Disponibilité créée avec succès", "fields": {"employee": "Employé", "dayOfWeek": "<PERSON><PERSON> <PERSON>", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "isRecurring": "<PERSON><PERSON><PERSON>", "recurrencePattern": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "type": "Type", "status": "Statut", "description": "Description", "exceptionDate": "Date d'exception", "isAvailable": "Disponible", "reason": "<PERSON>son"}, "daysOfWeek": {"sunday": "<PERSON><PERSON><PERSON>", "monday": "<PERSON><PERSON>", "tuesday": "<PERSON><PERSON>", "wednesday": "<PERSON><PERSON><PERSON><PERSON>", "thursday": "<PERSON><PERSON>", "friday": "<PERSON><PERSON><PERSON><PERSON>", "saturday": "<PERSON><PERSON>"}, "timeOffTypes": {"vacation": "Vacances", "sick_leave": "Con<PERSON> maladie", "personal_leave": "Congé personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}, "tabs": {"regularHours": "Heures régulières", "timeOff": "<PERSON><PERSON><PERSON>", "exceptions": "Exceptions"}, "actions": {"approve": "Approuver", "reject": "<PERSON><PERSON><PERSON>", "cancel": "Annuler"}, "featureErrors": {"invalidTimeRange": "Plage horaire invalide. L'heure de fin doit être après l'heure de début.", "invalidDateRange": "Plage de dates invalide. La date de fin doit être après la date de début.", "overlappingAvailability": "Cette disponibilité chevauche une disponibilité existante.", "overlappingTimeOff": "Cette demande de congé chevauche une demande existante."}, "regularHoursTitle": "Disponibilité Hebdomadaire", "regularHoursDescription": "Définir votre disponibilité hebdomadaire régulière", "timeOff": {"title": "<PERSON><PERSON><PERSON>", "createTitle": "<PERSON><PERSON><PERSON> un Con<PERSON>", "editTitle": "Modifier la Demande de Congé", "createDescription": "Soumettre une nouvelle demande de congé", "editDescription": "Modifier votre demande de congé", "listTitle": "<PERSON><PERSON><PERSON>", "noRecords": "Aucune demande de congé trouvée", "addButton": "<PERSON><PERSON><PERSON> un Con<PERSON>", "startDate": "Date de début", "endDate": "Date de fin", "startTime": "Heure de début (optionnel)", "endTime": "Heure de fin (optionnel)", "type": "Type", "selectType": "Sélectionner le type", "description": "Raison/Notes", "createSuccess": "<PERSON><PERSON><PERSON> de congé créée avec succès", "updateSuccess": "<PERSON><PERSON><PERSON> de congé mise à jour avec succès", "deleteSuccess": "<PERSON><PERSON><PERSON> de congé supprimée avec succès", "approveSuccess": "<PERSON><PERSON><PERSON> de congé approuvée", "rejectSuccess": "<PERSON><PERSON><PERSON> de congé rejetée", "cancelSuccess": "<PERSON><PERSON><PERSON> de congé annulée", "deleteConfirmTitle": "Su<PERSON><PERSON>er la Demande de Congé", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette demande de congé? Cette action ne peut pas être annulée.", "approveButton": "Approuver", "rejectButton": "<PERSON><PERSON><PERSON>", "cancelButton": "Annuler la Demande", "types": {"vacation": "Vacances", "sickLeave": "Congé Maladie", "personalLeave": "Congé Personnel", "other": "<PERSON><PERSON>"}, "status": {"pending": "En Attente", "approved": "Approu<PERSON><PERSON>", "rejected": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}, "exceptions": {"title": "Exceptions de Disponibilité", "createTitle": "<PERSON><PERSON><PERSON> une Exception", "editTitle": "Modifier l'Exception", "createDescription": "<PERSON><PERSON><PERSON> une exception ponctuelle à la disponibilité régulière", "editDescription": "Modifier l'exception de disponibilité", "listTitle": "Exceptions de Disponibilité", "noRecords": "Aucune exception trouvée", "addButton": "Ajouter une Exception", "date": "Date", "reason": "<PERSON>son", "reasonPlaceholder": "Expliquez pourquoi cette exception est nécessaire", "availableLabel": "Disponible", "unavailableLabel": "Indisponible", "createSuccess": "Exception créée avec succès", "updateSuccess": "Exception mise à jour avec succès", "deleteSuccess": "Exception supprimée avec succès", "deleteConfirmTitle": "Supprimer l'Exception", "deleteConfirmMessage": "Êtes-vous sûr de vouloir supprimer cette exception? Cette action ne peut pas être annulée."}}}, "contact": {"management": {"title": "Contacts", "description": "Gérer vos contacts", "createTitle": "<PERSON><PERSON><PERSON> un contact", "createDescription": "Ajouter un nouveau contact à votre organisation", "editTitle": "Modifier le contact", "editDescription": "Mettre à jour les informations du contact", "removeTitle": "Supprimer le contact", "removeDescription": "Êtes-vous sûr de vouloir supprimer ce contact?", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "backToList": "Retour à la liste", "itemUpdated": "Contact mis à jour avec succès", "noItems": "Aucun contact trouvé", "fields": {"name": "Nom", "type": "Type", "status": "Statut", "email": "<PERSON><PERSON><PERSON>", "emailPersonal": "<PERSON><PERSON><PERSON> (Personnel)", "emailWork": "<PERSON><PERSON><PERSON> (Travail)", "phone": "Téléphone", "phoneMobile": "Téléphone (Mobile)", "phoneHome": "Téléphone (Domicile)", "address": "<PERSON><PERSON><PERSON>", "notes": "Notes", "searchPlaceholder": "Rechercher des contacts...", "placeholders": {"name": "Entrez le nom", "emailPersonal": "Entrez le courriel personnel", "emailWork": "Entrez le courriel de travail", "phoneMobile": "Entrez le téléphone mobile", "phoneHome": "Entrez le téléphone domicile", "address": "Entrez l'adresse", "status": "Sélectionnez le statut"}}, "status": {"active": "Actif", "inactive": "Inactif", "archived": "Archivé", "draft": "Brouillon"}, "type": {"family": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "organization": "Organisation", "other": "<PERSON><PERSON>"}, "actions": {"create": "<PERSON><PERSON><PERSON>", "edit": "Modifier", "view": "Voir", "remove": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "removing": "Suppression en cours...", "save": "Enregistrer", "saving": "Enregistrement...", "cancel": "Annuler", "creating": "Création en cours..."}, "messages": {"removeWarning": "Cette action ne peut pas être annulée. Le contact sera marqué comme inactif."}, "history": {"title": "Historique", "description": "Changements récents sur ce contact", "contactHistoryTitle": "Historique du contact", "noChanges": "Aucun changement enregistré", "unknownUser": "Utilisateur inconnu", "recordDeleted": "L'enregistrement a été supprimé", "actions": {"insert": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Supprimé"}}, "relationships": {"title": "Relations", "description": "G<PERSON>rer les relations pour ce contact", "createTitle": "<PERSON><PERSON><PERSON> une relation", "createDescription": "Ajouter une nouvelle relation pour ce contact", "editTitle": "Modifier la relation", "editDescription": "Mettre à jour les informations de relation", "removeTitle": "Supprimer la relation", "removeDescription": "Êtes-vous sûr de vouloir supprimer cette relation?", "removeConfirm": "<PERSON><PERSON><PERSON><PERSON>", "backToContact": "Retour au contact", "noRelationships": "Aucune relation trouvée", "relationshipCreated": "Relation créée avec succès", "relationshipUpdated": "Relation mise à jour avec succès", "relationshipRemoved": "Relation supprimée avec succès", "unknownContact": "Contact inconnu", "fields": {"relatedContact": "Contact lié", "relationship": "Type de relation", "selectContact": "Sélectionner un contact", "selectRelationship": "Sélectionner un type de relation", "searchContacts": "Rechercher des contacts...", "noContactsFound": "Aucun contact trouvé", "minCharactersMessage": "Tapez au moins 2 caractères pour rechercher", "pleaseSelectContact": "Veuillez sélectionner un contact"}, "categories": {"family": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "other": "<PERSON><PERSON>"}, "types": {"parent": "Parent", "child": "<PERSON><PERSON>", "sibling": "<PERSON><PERSON>/Sœur", "spouse": "Conjoint(e)", "grandparent": "Grand-parent", "grandchild": "<PERSON><PERSON><PERSON><PERSON>", "aunt_uncle": "Tante/Oncle", "niece_nephew": "Nièce/Neveu", "cousin": "Cousin(e)", "step_parent": "Beau-parent", "step_child": "Beau-fils/Belle-fille", "guardian": "<PERSON><PERSON><PERSON>", "ward": "<PERSON><PERSON><PERSON>", "lawyer": "Avocat", "client": "Client", "social_worker": "Travailleur social", "case_manager": "Gestionnaire de cas", "doctor": "Médecin", "patient": "Patient", "therapist": "Thérapeute", "friend": "Ami(e)", "neighbor": "Voisin(e)", "colleague": "<PERSON><PERSON><PERSON><PERSON>", "other": "<PERSON><PERSON>"}}}}, "caseFile": {"active": {"title": "Dossier Actif", "description": "Gérer les informations et documents du dossier actif", "dashboard": {"title": "Tableau de bord", "quickActions": {"title": "Actions Rapides", "schedule": "Planifier", "addDocument": "Ajouter Document", "createNote": "<PERSON><PERSON>er Note", "viewHistory": "Voir Historique"}, "familyInformation": {"title": "Informations Familiales", "description": "Coordonnées et relations", "children": "<PERSON><PERSON><PERSON>", "adults": "Adultes", "professionals": "Professionnels", "noContacts": "Aucun contact trouvé", "noContactsDescription": "Ajoutez des contacts pour commencer", "viewAllContacts": "Voir Tous les Contacts"}, "serviceRequirements": {"title": "De<PERSON>e de service", "description": "<PERSON>g<PERSON><PERSON>, rendez-vous et éléments en attente", "viewDetails": "Voir la Demande Complète", "noServiceRequest": "Aucune demande de service trouvée", "noServiceRequestDescription": "Les informations de service apparaîtront ici une fois qu'une demande sera liée", "request": "<PERSON><PERSON><PERSON>", "minutes": "min", "startDate": "Date de début", "endDate": "Date de fin", "duration": "<PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON>", "location": "Emplacement"}, "appointmentSummary": {"title": "<PERSON><PERSON><PERSON>vous", "today": "<PERSON><PERSON><PERSON>'hui", "tomorrow": "<PERSON><PERSON><PERSON>", "upcoming": "À venir", "overdue": "En retard", "noAppointments": "Aucun rendez-vous à venir", "viewAll": "Voir Tous les Rendez-vous", "status": {"planned": "Planifié", "confirmed": "<PERSON><PERSON><PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON><PERSON>", "missed": "<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON>"}}}, "documents": {"title": "Documents", "caseDocuments": "Documents du Dossier", "contactDocuments": "Documents des Contacts", "noDocuments": "Aucun document trouvé", "noDocumentsDescription": "Téléchargez des documents pour commencer", "uploadDocument": "Télécharger Document", "viewDocument": "Voir Document", "downloadDocument": "Télécharger Document", "deleteDocument": "Supprimer Document", "documentUploaded": "Document téléchargé avec succès", "documentDeleted": "Document supprimé avec succès"}, "contacts": {"title": "<PERSON><PERSON> <PERSON> Do<PERSON>r", "addContact": "Ajouter Contact", "removeContact": "Supprimer Contact", "viewContact": "Voir Contact", "noContacts": "Aucun contact trouvé", "noContactsDescription": "Ajoutez des contacts pour commencer", "contactAdded": "Contact ajouté avec succès", "contactRemoved": "Contact supprimé avec succès", "removeContactConfirm": "Êtes-vous sûr de vouloir supprimer {name} de ce dossier?", "addContactModal": {"title": "Ajouter Contact au Dossier", "description": "Recherchez un contact et spécifiez sa relation pour l'ajouter à ce dossier.", "searchPlaceholder": "Rechercher un contact...", "relationshipType": "Type de Relation", "selectRelationshipType": "Sélectionner le type de relation", "addContact": "Ajouter Contact", "adding": "Ajout en cours...", "cancel": "Annuler"}, "relationshipTypes": {"parent": "Parent", "child": "<PERSON><PERSON>", "guardian": "<PERSON><PERSON><PERSON>", "sibling": "<PERSON><PERSON>/Sœur", "grandparent": "Grand-parent", "other_family": "Autre Famille", "social_worker": "Travailleur Social", "therapist": "Thérapeute", "teacher": "Enseignant", "doctor": "Médecin", "lawyer": "Avocat", "other_professional": "Autre Professionnel", "friend": "<PERSON><PERSON>", "neighbor": "Voisin", "other": "<PERSON><PERSON>"}, "contactInfo": {"email": "<PERSON><PERSON><PERSON>", "phone": "Téléphone", "address": "<PERSON><PERSON><PERSON>", "personalEmail": "<PERSON><PERSON><PERSON>", "workEmail": "<PERSON><PERSON><PERSON>", "mobilePhone": "Téléphone Mobile", "homePhone": "Téléphone Domicile", "workPhone": "Téléphone Travail", "noEmailsListed": "<PERSON><PERSON><PERSON> adresse co<PERSON>", "noPhoneNumbers": "Aucun numéro de téléphone listé", "noAddressProvided": "<PERSON><PERSON>ne adresse fournie"}, "contactDrawer": {"title": "<PERSON><PERSON><PERSON> du Contact", "contactInformation": "Informations de Contact", "close": "<PERSON><PERSON><PERSON>"}}, "history": {"title": "Historique du Dossier", "description": "Chronologie des modifications apportées à ce dossier", "noHistory": "Aucun historique trouvé", "noHistoryDescription": "Les modifications apparaîtront ici au fur et à mesure", "changeDetails": "Détails des Modifications", "viewDetails": "Voir Détails", "hideDetails": "Masquer Détails", "changedBy": "Modifié par", "changedAt": "<PERSON><PERSON><PERSON><PERSON> le", "actions": {"INSERT": "<PERSON><PERSON><PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON>", "DELETE": "Supprimé"}}, "tabs": {"dashboard": "Tableau de bord", "serviceRequest": "De<PERSON>e de service", "documents": "Documents", "contacts": "Contacts", "appointments": "<PERSON><PERSON><PERSON>vous", "history": "Historique"}, "serviceRequest": {"title": "De<PERSON>e de service", "description": "Informations complètes sur la demande de service et les exigences", "serviceDetails": "Détails du service", "requestDetails": "<PERSON><PERSON><PERSON> de la demande", "locationDetails": "Détails de l'emplacement", "contactDetails": "<PERSON><PERSON><PERSON> du contact", "scheduleDetails": "Détails de l'horaire", "requirements": "Exigences", "noRequest": "Aucune demande de service", "noRequestDescription": "Aucune information de demande de service n'est disponible pour ce dossier.", "serviceName": "Nom du service", "serviceDescription": "Description du service", "requestNumber": "Numéro de demande", "requestTitle": "<PERSON><PERSON><PERSON> de la demande", "requestDescription": "Description de la demande", "startDate": "Date de début", "endDate": "Date de fin", "duration": "<PERSON><PERSON><PERSON>", "frequency": "<PERSON><PERSON><PERSON>", "location": "Emplacement", "address": "<PERSON><PERSON><PERSON>", "contactPerson": "<PERSON><PERSON> de contact", "phone": "Téléphone", "email": "<PERSON><PERSON><PERSON>", "status": "Statut", "priority": "Priorité", "objectives": "Objectifs", "specialRequirements": "Exigences spéciales", "frequencyCount": "Nombre de fréquences"}, "appointments": {"title": "<PERSON><PERSON><PERSON>vous du dossier", "description": "Rendez-vous associés à ce dossier", "noAppointments": "Aucun rendez-vous trouvé", "noAppointmentsDescription": "Aucun rendez-vous n'a encore été planifié pour ce dossier", "viewAppointment": "Voir le rendez-vous", "scheduleNew": "Planifier un nouveau rendez-vous", "form": {"newAppointmentDetails": "Détails du nouveau rendez-vous", "editAppointmentDetails": "Modifier les détails du rendez-vous", "appointmentDetails": "<PERSON><PERSON><PERSON> du rendez-vous", "fillDetails": "Remplis<PERSON>z les détails du rendez-vous ci-dessous", "updateDetails": "Met<PERSON>z à jour les détails du rendez-vous ci-dessous", "viewDetails": "Voir les détails et informations du rendez-vous", "appointmentTitle": "Titre du rendez-vous", "appointmentTitleRequired": "Titre du rendez-vous *", "description": "Description", "enterTitle": "Entrez le titre du rendez-vous", "enterDescription": "Entrez la description du rendez-vous", "serviceType": "Type de service (du dossier)", "serviceAutoSet": "Le type de service est automatiquement défini à partir du dossier et ne peut pas être modifié.", "startTime": "<PERSON><PERSON> d<PERSON>", "startTimeRequired": "<PERSON><PERSON> de d<PERSON> *", "endTime": "Heure de fin", "endTimeRequired": "Heure de fin *", "room": "Salle", "selectRoom": "<PERSON><PERSON><PERSON><PERSON>ner une salle", "loadingRooms": "Chargement des salles...", "noRoomsAvailable": "Aucune salle disponible", "capacity": "Capacité", "staffAssignment": "Assignation du personnel", "selectEmployee": "Sélectionner un employé à assigner", "noEmployeesFound": "<PERSON><PERSON><PERSON> <PERSON><PERSON> trouvé", "noTitle": "Aucun titre", "assignedStaff": "Personnel assigné", "noStaffAssigned": "Aucun personnel assigné pour le moment. Sélectionnez un employé ci-dessus pour l'assigner.", "cancel": "Annuler", "save": "Enregistrer le rendez-vous", "update": "Mettre à jour le rendez-vous", "creating": "Création...", "updating": "Mise à jour...", "primary": "Principal"}, "view": {"date": "Date", "status": "Statut", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "duration": "<PERSON><PERSON><PERSON>", "room": "Salle", "noRoomAssigned": "<PERSON><PERSON><PERSON> salle <PERSON>", "assignedStaff": "Personnel assigné", "noStaffAssigned": "Aucun personnel assigné à ce rendez-vous", "quickActions": "Actions rapides", "complete": "<PERSON><PERSON><PERSON>", "markMissed": "<PERSON><PERSON> man<PERSON>", "postpone": "Reporter", "cancel": "Annuler", "close": "<PERSON><PERSON><PERSON>", "editAppointment": "Modifier le rendez-vous", "statusMessage": "Ce rendez-vous est {status}. Les changements de statut ne sont disponibles que pour les rendez-vous planifiés ou confirmés.", "staff": "Personnel", "more": "de plus"}, "sidebar": {"selectDate": "Sélectionner une date", "changeDate": "Changer la date", "appointmentDate": "Date du rendez-vous", "selectDateDescription": "Sélectionnez une date pour votre rendez-vous", "changeDateDescription": "Changer la date du rendez-vous", "scheduledDescription": "<PERSON><PERSON>-vous planifié pour cette date", "selected": "Sélectionné:", "newDate": "Nouvelle date:", "scheduled": "Planifié:", "createHint": "💡 Choisissez une date, puis remplissez le formulaire", "editHint": "✏️ Sélectionnez une nouvelle date ou gardez l'actuelle", "viewHint": "📅 Date du rendez-vous affichée ci-dessus"}, "validation": {"titleRequired": "Le titre est requis", "dateRequired": "<PERSON><PERSON><PERSON>z sélectionner une date", "startTimeRequired": "L'heure de début est requise", "endTimeRequired": "L'heure de fin est requise", "endTimeAfterStart": "L'heure de fin doit être après l'heure de début", "employeeAlreadyAssigned": "L'employé est déjà assigné à ce rendez-vous", "selectDateTime": "Veuillez sélectionner la date et l'heure du rendez-vous avant d'assigner le personnel", "employeeNotAvailable": "{name} n'est pas disponible pour la date et l'heure sélectionnées. Veuillez choisir un autre employé ou créneau horaire.", "roomNotAvailable": "{room} n'est pas disponible pour la date et l'heure sélectionnées. Veuillez choisir une autre salle ou créneau horaire."}, "success": {"appointmentCreated": "<PERSON><PERSON><PERSON>vous créé avec succès!", "appointmentUpdated": "<PERSON><PERSON>-vous mis à jour avec succès!", "employeeAssigned": "{name} a été assigné à ce rendez-vous", "statusUpdated": "<PERSON><PERSON><PERSON>vous marqué comme {status}"}, "errors": {"createFailed": "Échec de la création du rendez-vous", "updateFailed": "Échec de la mise à jour du rendez-vous", "statusUpdateFailed": "Échec de la mise à jour du statut du rendez-vous", "genericError": "Une erreur s'est produite"}}}}}