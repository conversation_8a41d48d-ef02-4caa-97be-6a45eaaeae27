"use server";

import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";

export interface OrganizationRoom {
  id: string;
  name: string;
  description: string | null;
  capacity: number | null;
  status: string;
  location_id: string;
  location_name: string;
}

export async function getOrganizationRooms(
  organizationId: string
): Promise<{ success: boolean; data?: OrganizationRoom[]; error?: string }> {
  try {
    const supabase = await createClient();
    const currentUser = await auth.getCurrentUser();

    if (!currentUser) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // Get all active rooms for the organization with location information
    const { data: rooms, error } = await supabase
      .from("rooms")
      .select(`
        id,
        name,
        description,
        capacity,
        status,
        location_id,
        locations!rooms_location_id_fkey(
          id,
          name
        )
      `)
      .eq("organization_id", organizationId)
      .eq("status", "active")
      .order("name");

    if (error) {
      return {
        success: false,
        error: `Failed to fetch rooms: ${error.message}`,
      };
    }

    // Transform the data to include location name
    const transformedRooms: OrganizationRoom[] = (rooms || []).map((room: any) => ({
      id: room.id,
      name: room.name,
      description: room.description,
      capacity: room.capacity,
      status: room.status,
      location_id: room.location_id,
      location_name: room.locations?.name || "Unknown Location",
    }));

    return {
      success: true,
      data: transformedRooms,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch organization rooms",
    };
  }
}
