import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TableCell, TableRow } from "@/components/ui/table";
import { Eye, Edit, Calendar, Clock } from "lucide-react";
import { AppointmentWithDetails } from "../lib/types";
import { AppointmentStatusBadge } from "./AppointmentStatusBadge";
import { Dictionary } from "@/lib/i18n/services/I18nService";

interface AppointmentTableRowProps {
  appointment: AppointmentWithDetails;
  dictionary: Dictionary;
}

/**
 * Table row component for appointment list
 * Displays appointment information in a table format
 */
export function AppointmentTableRow({ appointment, dictionary }: AppointmentTableRowProps) {
  return (
    <TableRow className="hover:bg-muted/50">
      {/* Title and Description */}
      <TableCell>
        <div className="space-y-1">
          <p className="font-medium">{appointment.title}</p>
          {appointment.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">{appointment.description}</p>
          )}
        </div>
      </TableCell>

      {/* Date */}
      <TableCell>
        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{appointment.date_display}</span>
        </div>
      </TableCell>

      {/* Time */}
      <TableCell>
        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm">{appointment.time_display}</span>
        </div>
      </TableCell>

      {/* Duration */}
      <TableCell>
        <span className="text-sm">{appointment.duration_display}</span>
      </TableCell>

      {/* Status */}
      <TableCell>
        <AppointmentStatusBadge status={appointment.status} dictionary={dictionary} />
      </TableCell>

      {/* Created Date */}
      <TableCell>
        <span className="text-sm text-muted-foreground">
          {appointment.created_at ? new Date(appointment.created_at).toLocaleDateString() : "N/A"}
        </span>
      </TableCell>

      {/* Actions */}
      <TableCell>
        <div className="flex items-center gap-1">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`${appointment.id}/view`}>
              <Eye className="h-4 w-4" />
              <span className="sr-only">{dictionary.appointments.actions.view}</span>
            </Link>
          </Button>
          <Button variant="ghost" size="sm" asChild>
            <Link href={`${appointment.id}/edit`}>
              <Edit className="h-4 w-4" />
              <span className="sr-only">{dictionary.appointments.actions.edit}</span>
            </Link>
          </Button>
        </div>
      </TableCell>
    </TableRow>
  );
}
