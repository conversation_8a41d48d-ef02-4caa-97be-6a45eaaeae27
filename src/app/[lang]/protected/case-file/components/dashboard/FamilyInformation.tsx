import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { H2, P } from "@/components/typography";
import { Users } from "lucide-react";
import { CaseFileDashboardData } from "../../lib/types";

interface FamilyInformationProps {
  familyInfo: CaseFileDashboardData["familyInfo"];
  caseFileId: string;
  lang: string;
  dictionary?: {
    title: string;
    description: string;
  };
}

/**
 * Family Information component for case file dashboard
 * Displays primary contact and children information
 */
export function FamilyInformation({
  familyInfo,
  caseFileId: _caseFileId,
  lang: _lang,
  dictionary,
}: FamilyInformationProps) {
  // Default translations
  const defaultDictionary = {
    title: "Family Information",
    description: "Contact details and relationships",
  };

  const t = dictionary || defaultDictionary;
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Users className="mr-2" />
          {t.title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Primary Contact */}
        {familyInfo.primaryContact ? (
          <div>
            <H2>{familyInfo.primaryContact.name}</H2>
            <P>{familyInfo.primaryContact.relationship}</P>
            <div>
              <P>{familyInfo.primaryContact.phone || "No phone"}</P>
              <P>{familyInfo.primaryContact.email || "No email"}</P>
            </div>
          </div>
        ) : (
          <div>
            <H2>No Primary Contact</H2>
            <P>No primary contact assigned</P>
          </div>
        )}

        {/* Children */}
        <div>
          <H2>Children</H2>
          {familyInfo.children.length > 0 ? (
            <div>
              {familyInfo.children.map((child, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span>{child.name}</span>
                  {child.age && <Badge variant="secondary">{child.age} years old</Badge>}
                </div>
              ))}
            </div>
          ) : (
            <P>No children listed</P>
          )}
        </div>

        <div>
          <Button variant="outline">View All Contacts ({familyInfo.totalContacts})</Button>
        </div>
      </CardContent>
    </Card>
  );
}
