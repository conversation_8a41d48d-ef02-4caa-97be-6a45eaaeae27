"use server";

import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";

export async function saveAppointmentRoom(
  appointmentId: string,
  roomId: string | null,
  organizationId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createClient();
    const userProfile = await auth();

    if (!userProfile) {
      return {
        success: false,
        error: "Authentication required",
      };
    }

    // First, remove any existing room assignment for this appointment
    const { error: deleteError } = await supabase
      .from("appointment_rooms")
      .delete()
      .eq("appointment_id", appointmentId)
      .eq("organization_id", organizationId);

    if (deleteError) {
      return {
        success: false,
        error: `Failed to remove existing room assignment: ${deleteError.message}`,
      };
    }

    // If a room is specified, create the new assignment
    if (roomId) {
      const { error: insertError } = await supabase
        .from("appointment_rooms")
        .insert({
          appointment_id: appointmentId,
          room_id: roomId,
          organization_id: organizationId,
          reserved_by: userProfile.userId,
        });

      if (insertError) {
        return {
          success: false,
          error: `Failed to assign room: ${insertError.message}`,
        };
      }
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save room assignment",
    };
  }
}

export async function getAppointmentRoom(
  appointmentId: string,
  organizationId: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const supabase = await createClient();
    
    const { data, error } = await supabase
      .from("appointment_rooms")
      .select(`
        id,
        room_id,
        rooms!appointment_rooms_room_id_fkey(
          id,
          name,
          description,
          capacity,
          status
        )
      `)
      .eq("appointment_id", appointmentId)
      .eq("organization_id", organizationId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      return {
        success: false,
        error: `Failed to load room assignment: ${error.message}`,
      };
    }

    return {
      success: true,
      data: data || null,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to load room assignment",
    };
  }
}
