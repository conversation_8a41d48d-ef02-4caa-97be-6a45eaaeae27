import { createClient } from "@/lib/supabase/server";
import { auth } from "@/lib/authentication/services/AuthenticationService";

export interface TodayAppointment {
  id: string;
  title: string;
  time: string;
  duration: string;
  status: string;
  room?: string;
  location?: string;
  case_file_id?: string | null;
}

export class DashboardService {
  private static instance: DashboardService;

  private constructor() {}

  public static getInstance(): DashboardService {
    if (!DashboardService.instance) {
      DashboardService.instance = new DashboardService();
    }
    return DashboardService.instance;
  }

  /**
   * Get today's appointments for the current user
   */
  async getTodayAppointments(): Promise<{
    success: boolean;
    data?: TodayAppointment[];
    error?: string;
  }> {
    try {
      // Get current user and organization
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();
      const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

      // Query appointments for today where the current user is assigned
      const { data, error } = await supabase
        .from("appointments")
        .select(`
          id,
          title,
          description,
          appointment_date,
          start_time,
          end_time,
          duration_minutes,
          status,
          case_file_id,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            id,
            employees!appointment_assignments_employee_id_fkey(
              id,
              user_id,
              first_name,
              last_name
            )
          ),
          appointment_rooms!appointment_rooms_appointment_id_fkey(
            id,
            rooms!appointment_rooms_room_id_fkey(
              id,
              name,
              locations!rooms_location_id_fkey(
                id,
                name
              )
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .eq("appointment_date", today)
        .order("start_time", { ascending: true });

      if (error) {
        return {
          success: false,
          error: `Failed to fetch appointments: ${error.message}`,
        };
      }

      // Filter appointments where current user is assigned
      const userAppointments = (data || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      });

      // Transform to simplified format for dashboard
      const todayAppointments: TodayAppointment[] = userAppointments.map((appointment) => {
        const room = appointment.appointment_rooms?.[0]?.rooms;
        const location = room?.locations?.name;

        return {
          id: appointment.id,
          title: appointment.title,
          time: appointment.start_time,
          duration: `${appointment.duration_minutes}min`,
          status: appointment.status,
          room: room?.name,
          location: location,
          case_file_id: appointment.case_file_id,
        };
      });

      return {
        success: true,
        data: todayAppointments,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  /**
   * Get user's workload summary
   */
  async getWorkloadSummary(): Promise<{
    success: boolean;
    data?: {
      activeCases: number;
      pendingRequests: number;
      upcomingAppointments: number;
      overdueItems: number;
    };
    error?: string;
  }> {
    try {
      const currentUser = await auth.getCurrentUser();
      if (!currentUser) {
        return {
          success: false,
          error: "Authentication required",
        };
      }

      const userProfile = await auth.getCurrentUserProfile();
      if (!userProfile?.organizationId) {
        return {
          success: false,
          error: "Organization context required",
        };
      }

      const supabase = await createClient();
      const today = new Date().toISOString().split('T')[0];

      // Get active cases count (simplified - would need proper case assignment logic)
      const { count: activeCases } = await supabase
        .from("case_files")
        .select("*", { count: "exact", head: true })
        .eq("organization_id", userProfile.organizationId)
        .eq("status", "active");

      // Get upcoming appointments count (next 7 days)
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const nextWeekStr = nextWeek.toISOString().split('T')[0];

      const { data: upcomingAppointmentsData } = await supabase
        .from("appointments")
        .select(`
          id,
          appointment_assignments!appointment_assignments_appointment_id_fkey(
            employees!appointment_assignments_employee_id_fkey(
              user_id
            )
          )
        `)
        .eq("organization_id", userProfile.organizationId)
        .gte("appointment_date", today)
        .lte("appointment_date", nextWeekStr);

      const upcomingAppointments = (upcomingAppointmentsData || []).filter((appointment) => {
        return appointment.appointment_assignments?.some((assignment: any) =>
          assignment.employees?.user_id === currentUser.id
        );
      }).length;

      // Mock data for pending requests and overdue items
      // These would need proper implementation based on your business logic
      const pendingRequests = 5;
      const overdueItems = 3;

      return {
        success: true,
        data: {
          activeCases: activeCases || 0,
          pendingRequests,
          upcomingAppointments,
          overdueItems,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }
}

export const dashboardService = DashboardService.getInstance();
