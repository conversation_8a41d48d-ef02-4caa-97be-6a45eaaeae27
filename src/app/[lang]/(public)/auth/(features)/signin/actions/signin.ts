"use server";

import { auth } from "@/lib/authentication/services/AuthenticationService";
import { logger } from "@/lib/logger/services/LoggerService";
import { redirect } from "next/navigation";

// Define the state type
interface SignInState {
  success: boolean;
  error: string | null;
}

// Server Action for form submission
export async function signIn(_prevState: SignInState, formData: FormData) {
  // Variable to store redirect URL
  let shouldRedirect = false;

  const email = formData.get("email") as string;
  const password = formData.get("password") as string;
  // We get the lang but don't use it currently
  // const lang = formData.get("lang") as string;

  logger.info(`Attempting to sign in user: ${email}`);
  const { error } = await auth.signIn(email, password);

  // Handle authentication error
  if (error) {
    logger.error(`Sign in error: ${error.message}`);
    return {
      success: false,
      error: error.message,
    };
  }

  logger.info(`User signed in successfully: ${email}`);

  // Set redirect flag
  shouldRedirect = true;

  // Return success state (this will only be used if redirect doesn't happen)
  const result = {
    success: true,
    error: null,
  };

  // Redirect outside of any try-catch
  if (shouldRedirect) {
    redirect(`/fr/protected/dashboard`);
  }

  return result;
}
