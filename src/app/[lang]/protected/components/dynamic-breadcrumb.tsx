"use client";

import { usePathname } from "next/navigation";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON>rumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Link from "next/link";
import { Home } from "lucide-react";

interface DynamicBreadcrumbProps {
  lang: string;
}

interface BreadcrumbItem {
  label: string;
  href: string;
  isLast: boolean;
  isHome?: boolean;
}

// Function to check if a string is a UUID
function isUUID(str: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
}

// Function to generate breadcrumbs from URL path
function generateBreadcrumbs(pathname: string, lang: string) {
  // Strip the prefix /[lang]/protected
  const prefix = `/${lang}/protected`;
  const pathWithoutPrefix = pathname.startsWith(prefix) ? pathname.slice(prefix.length) : pathname;

  // If we're at the root protected route, just show Dashboard
  if (!pathWithoutPrefix || pathWithoutPrefix === "/") {
    return [
      {
        label: "Dashboard",
        href: `/${lang}/protected/dashboard`,
        isLast: true,
        isHome: true,
      },
    ];
  }

  // Tokenize by delimiter /
  const segments = pathWithoutPrefix.split("/").filter((segment) => segment.length > 0);

  // Build breadcrumbs
  const breadcrumbs = [
    {
      label: "Dashboard",
      href: `/${lang}/protected/dashboard`,
      isLast: false,
      isHome: true,
    },
  ];

  // Add each segment as a breadcrumb, building href progressively
  let currentPath = "";
  segments.forEach((segment) => {
    // Always add segment to the current path for href construction
    currentPath += `/${segment}`;
    const href = `/${lang}/protected${currentPath}`;

    // Skip UUIDs in breadcrumb display, but keep them in the href
    if (isUUID(segment)) {
      // Don't add UUID segments to breadcrumbs display
      return;
    }

    // Capitalize and format the segment name
    const label = segment
      .split("-")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");

    breadcrumbs.push({
      label,
      href,
      isLast: false, // We'll update this after filtering
      isHome: false,
    });
  });

  // Update the last breadcrumb to be marked as last
  if (breadcrumbs.length > 0) {
    breadcrumbs[breadcrumbs.length - 1].isLast = true;
  }

  return breadcrumbs;
}

export function DynamicBreadcrumb({ lang }: DynamicBreadcrumbProps) {
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, lang);

  return (
    <Breadcrumb>
      <BreadcrumbList>
        {breadcrumbs.map((breadcrumb, index) => (
          <div key={`${breadcrumb.href}-${index}`} className="flex items-center">
            {index > 0 && <BreadcrumbSeparator />}
            <BreadcrumbItem>
              {breadcrumb.isLast ? (
                <BreadcrumbPage>
                  {breadcrumb.isHome ? <Home className="h-4 w-4" /> : breadcrumb.label}
                </BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link href={breadcrumb.href}>
                    {breadcrumb.isHome ? <Home className="h-4 w-4" /> : breadcrumb.label}
                  </Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </div>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
