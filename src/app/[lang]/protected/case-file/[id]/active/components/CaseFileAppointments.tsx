"use client";

import { useState, useTransition } from "react";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, Clock, Eye, Plus, X, Save, Loader2, AlertCircle, CheckCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AppointmentWithDetails } from "../../../../scheduling/appointments/lib/types";
import { createCaseFileAppointment } from "../../../actions/create-appointment";
import Link from "next/link";

interface CaseFileAppointmentsProps {
  caseFileId: string;
  appointments: AppointmentWithDetails[];
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    noAppointments: string;
    noAppointmentsDescription: string;
    viewAppointment: string;
    scheduleNew: string;
  };
}

const defaultDictionary = {
  title: "Case File Appointments",
  description: "Appointments associated with this case file",
  noAppointments: "No appointments found",
  noAppointmentsDescription: "No appointments have been scheduled for this case file yet",
  viewAppointment: "View Appointment",
  scheduleNew: "Schedule New Appointment",
};

export function CaseFileAppointments({
  caseFileId,
  appointments,
  lang,
  dictionary,
}: CaseFileAppointmentsProps) {
  const t = dictionary || defaultDictionary;
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [sidebarMode, setSidebarMode] = useState<"create" | "view">("create");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [viewingAppointment, setViewingAppointment] = useState<AppointmentWithDetails | null>(null);
  const [appointmentForm, setAppointmentForm] = useState({
    title: "",
    description: "",
    start_time: "",
    end_time: "",
  });
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  const handleFormChange = (field: string, value: string) => {
    setAppointmentForm(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleSaveAppointment = async () => {
    // Clear previous messages
    setError(null);
    setSuccess(null);

    // Basic validation
    if (!appointmentForm.title.trim()) {
      setError("Title is required");
      return;
    }
    if (!selectedDate) {
      setError("Please select a date");
      return;
    }
    if (!appointmentForm.start_time) {
      setError("Start time is required");
      return;
    }
    if (!appointmentForm.end_time) {
      setError("End time is required");
      return;
    }

    // Validate time logic
    if (appointmentForm.end_time <= appointmentForm.start_time) {
      setError("End time must be after start time");
      return;
    }

    startTransition(async () => {
      try {
        // Create FormData for the appointment
        const formData = new FormData();
        formData.append("title", appointmentForm.title.trim());
        formData.append("description", appointmentForm.description.trim());
        formData.append("appointment_date", selectedDate.toISOString().split('T')[0]);
        formData.append("start_time", appointmentForm.start_time);
        formData.append("end_time", appointmentForm.end_time);
        formData.append("status", "planned");
        formData.append("case_file_id", caseFileId);

        const result = await createCaseFileAppointment(formData);

        if (result.success) {
          setSuccess("Appointment created successfully!");
          // Reset form
          setAppointmentForm({
            title: "",
            description: "",
            start_time: "",
            end_time: "",
          });
          setSelectedDate(new Date());
          // Close sidebar after a brief delay to show success message
          setTimeout(() => {
            setIsSidebarOpen(false);
            setSuccess(null);
            // Refresh the page to show the new appointment
            window.location.reload();
          }, 1500);
        } else {
          setError(result.error || "Failed to create appointment");
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "missed":
        return "bg-orange-100 text-orange-800";
      case "postponed":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex">
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`transition-all duration-500 ease-in-out ${isSidebarOpen ? 'flex-1 mr-6' : 'w-full'}`}
      >
        <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <CardTitle>{isSidebarOpen ? "New Appointment Details" : t.title}</CardTitle>
            </div>
            {!isSidebarOpen && (
              <Button size="sm" onClick={() => setIsSidebarOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t.scheduleNew}
              </Button>
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            {isSidebarOpen ? "Fill in the appointment details below" : t.description}
          </p>
        </CardHeader>
        <CardContent>
          {isSidebarOpen ? (
            // Appointment Form
            <div className="space-y-6">
              {/* Error/Success Messages */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50 text-green-800">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="title">Appointment Title *</Label>
                <Input
                  id="title"
                  value={appointmentForm.title}
                  onChange={(e) => handleFormChange("title", e.target.value)}
                  placeholder="Enter appointment title"
                  disabled={isPending}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={appointmentForm.description}
                  onChange={(e) => handleFormChange("description", e.target.value)}
                  placeholder="Enter appointment description"
                  rows={3}
                  disabled={isPending}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_time">Start Time *</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={appointmentForm.start_time}
                    onChange={(e) => handleFormChange("start_time", e.target.value)}
                    disabled={isPending}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="end_time">End Time *</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={appointmentForm.end_time}
                    onChange={(e) => handleFormChange("end_time", e.target.value)}
                    disabled={isPending}
                  />
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setIsSidebarOpen(false)}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveAppointment}
                  disabled={isPending}
                >
                  {isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Appointment
                    </>
                  )}
                </Button>
              </div>
            </div>
          ) : appointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">{t.noAppointments}</h3>
              <p className="text-muted-foreground mb-4">{t.noAppointmentsDescription}</p>
              <Button onClick={() => setIsSidebarOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t.scheduleNew}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {appointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{appointment.title}</h4>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status_display || appointment.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {appointment.date_display}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {appointment.time_display}
                      </div>
                      <div>{appointment.duration_display}</div>
                    </div>
                    {appointment.description && (
                      <p className="text-sm text-muted-foreground mt-2">
                        {appointment.description}
                      </p>
                    )}
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/${lang}/protected/scheduling/appointments/${appointment.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      {t.viewAppointment}
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
        </Card>
      </motion.div>

      {/* Secondary Sidebar for Appointment Creation */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="w-96 bg-background border-l shadow-lg flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Select Date</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Calendar Content */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Select a date for your appointment
                </div>

                <CalendarComponent
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  className="rounded-md border"
                />

                {selectedDate && (
                  <div className="p-3 bg-muted rounded-lg">
                    <p className="text-sm font-medium">Selected Date:</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedDate.toLocaleDateString()}
                    </p>
                  </div>
                )}

                <div className="text-xs text-muted-foreground">
                  💡 Select a date, then fill in the appointment details on the left
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
