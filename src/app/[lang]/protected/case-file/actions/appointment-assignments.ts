"use server";

import { AssignmentService } from "@/services/scheduling/AssignmentService";

export interface AppointmentAssignmentData {
  employeeId: string;
  assignmentRole?: string;
}

export async function saveAppointmentAssignments(
  appointmentId: string,
  assignments: AppointmentAssignmentData[],
  organizationId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const assignmentService = new AssignmentService();

    // First, get existing assignments and remove them
    const existingAssignments = await assignmentService.getAppointmentAssignments(appointmentId, organizationId);
    if (existingAssignments.success && existingAssignments.data) {
      for (const assignment of existingAssignments.data) {
        await assignmentService.removeAssignment(assignment.id, organizationId);
      }
    }

    // Then, add the new assignments
    if (assignments.length > 0) {
      const employeeAssignments = assignments.map(assignment => ({
        employeeId: assignment.employeeId,
        assignmentRole: assignment.assignmentRole || "primary",
      }));

      const bulkResult = await assignmentService.bulkAssignEmployees(
        appointmentId,
        employeeAssignments,
        organizationId
      );

      if (!bulkResult.success) {
        return {
          success: false,
          error: typeof bulkResult.error === 'string' ? bulkResult.error : "Failed to save assignments",
        };
      }
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save assignments",
    };
  }
}

export async function getAppointmentAssignments(
  appointmentId: string,
  organizationId: string
): Promise<{ success: boolean; data?: any[]; error?: string }> {
  try {
    const assignmentService = new AssignmentService();

    const result = await assignmentService.getAppointmentAssignments(appointmentId, organizationId);

    if (result.success) {
      return {
        success: true,
        data: result.data || [],
      };
    } else {
      return {
        success: false,
        error: typeof result.error === 'string' ? result.error : "Failed to load assignments",
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to load assignments",
    };
  }
}
