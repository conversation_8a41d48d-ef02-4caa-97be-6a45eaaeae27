"use client";

import { useState, useTransition } from "react";
import { <PERSON>, <PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar, Clock, Eye, Plus, X, Save, Loader2, AlertCircle, CheckCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AppointmentWithDetails } from "../../../../scheduling/appointments/lib/types";
import { createCaseFileAppointment, updateCaseFileAppointment } from "../../../actions/create-appointment";

interface CaseFileAppointmentsProps {
  caseFileId: string;
  appointments: AppointmentWithDetails[];
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    noAppointments: string;
    noAppointmentsDescription: string;
    viewAppointment: string;
    scheduleNew: string;
  };
}

const defaultDictionary = {
  title: "Case File Appointments",
  description: "Appointments associated with this case file",
  noAppointments: "No appointments found",
  noAppointmentsDescription: "No appointments have been scheduled for this case file yet",
  viewAppointment: "View Appointment",
  scheduleNew: "Schedule New Appointment",
};

export function CaseFileAppointments({
  caseFileId,
  appointments,
  lang: _lang,
  dictionary,
}: CaseFileAppointmentsProps) {
  const t = dictionary || defaultDictionary;
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [sidebarMode, setSidebarMode] = useState<"create" | "view" | "edit">("create");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [viewingAppointment, setViewingAppointment] = useState<AppointmentWithDetails | null>(null);
  const [appointmentForm, setAppointmentForm] = useState({
    title: "",
    description: "",
    start_time: "",
    end_time: "",
    service_id: "",
    room_id: "",
    participants: [] as string[],
  });
  const [isPending, startTransition] = useTransition();
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Mock data for UI demonstration
  const mockServices = [
    { id: "1", name: "Family Supervision Visit", description: "Supervised family visit" },
    { id: "2", name: "Case Assessment", description: "Family assessment session" },
    { id: "3", name: "Therapy Session", description: "Family therapy session" },
    { id: "4", name: "Court Preparation", description: "Court preparation meeting" },
  ];

  const mockParticipants = [
    { id: "1", name: "Marie Dubois", role: "Mother", type: "parent" },
    { id: "2", name: "Jean Dubois", role: "Father", type: "parent" },
    { id: "3", name: "Sophie Dubois", role: "Child (8 years)", type: "child" },
    { id: "4", name: "Lucas Dubois", role: "Child (5 years)", type: "child" },
    { id: "5", name: "Sarah Johnson", role: "Social Worker", type: "staff" },
  ];

  const mockRooms = [
    { id: "1", name: "Family Room A", capacity: 6, features: ["Video recording", "Toys"] },
    { id: "2", name: "Family Room B", capacity: 4, features: ["One-way mirror"] },
    { id: "3", name: "Conference Room 1", capacity: 8, features: ["Projector", "Whiteboard"] },
    { id: "4", name: "Therapy Room", capacity: 4, features: ["Comfortable seating", "Privacy"] },
  ];

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  const handleViewAppointment = (appointment: AppointmentWithDetails) => {
    setViewingAppointment(appointment);
    setSidebarMode("view");
    // Set the calendar to the appointment date
    setSelectedDate(new Date(appointment.appointment_date));
    setIsSidebarOpen(true);
  };

  const handleCreateAppointment = () => {
    setViewingAppointment(null);
    setSidebarMode("create");
    setSelectedDate(new Date());
    setAppointmentForm({
      title: "",
      description: "",
      start_time: "",
      end_time: "",
      service_id: "1", // Auto-populate from case file service
      room_id: "",
      participants: [],
    });
    setIsSidebarOpen(true);
  };

  const handleEditAppointment = (appointment: AppointmentWithDetails) => {
    setViewingAppointment(appointment);
    setSidebarMode("edit");
    setSelectedDate(new Date(appointment.appointment_date));
    setAppointmentForm({
      title: appointment.title,
      description: appointment.description || "",
      start_time: appointment.start_time,
      end_time: appointment.end_time,
      service_id: "1", // Auto-populate from case file service (same for all appointments)
      room_id: "2", // Mock: Get from appointment data
      participants: ["1", "3", "5"], // Mock: Get from appointment data
    });
    setIsSidebarOpen(true);
  };

  const handleFormChange = (field: string, value: string | string[]) => {
    setAppointmentForm(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (error) setError(null);
    if (success) setSuccess(null);
  };

  const handleParticipantToggle = (participantId: string) => {
    setAppointmentForm(prev => ({
      ...prev,
      participants: prev.participants.includes(participantId)
        ? prev.participants.filter(id => id !== participantId)
        : [...prev.participants, participantId]
    }));
  };

  // Helper functions for display
  const getServiceName = (serviceId: string) => {
    return mockServices.find(s => s.id === serviceId)?.name || "Unknown Service";
  };

  const getParticipantName = (participantId: string) => {
    return mockParticipants.find(p => p.id === participantId)?.name || "Unknown Participant";
  };

  const getRoomName = (roomId: string) => {
    return mockRooms.find(r => r.id === roomId)?.name || "No Room Selected";
  };

  const getSelectedParticipants = () => {
    return mockParticipants.filter(p => appointmentForm.participants.includes(p.id));
  };

  const handleSaveAppointment = async () => {
    // Clear previous messages
    setError(null);
    setSuccess(null);

    // Basic validation
    if (!appointmentForm.title.trim()) {
      setError("Title is required");
      return;
    }
    if (!selectedDate) {
      setError("Please select a date");
      return;
    }
    if (!appointmentForm.start_time) {
      setError("Start time is required");
      return;
    }
    if (!appointmentForm.end_time) {
      setError("End time is required");
      return;
    }

    // Validate time logic
    if (appointmentForm.end_time <= appointmentForm.start_time) {
      setError("End time must be after start time");
      return;
    }

    startTransition(async () => {
      try {
        // Create FormData for the appointment
        const formData = new FormData();
        formData.append("title", appointmentForm.title.trim());
        formData.append("description", appointmentForm.description.trim());
        formData.append("appointment_date", selectedDate.toISOString().split('T')[0]);
        formData.append("start_time", appointmentForm.start_time);
        formData.append("end_time", appointmentForm.end_time);

        if (sidebarMode === "edit" && viewingAppointment) {
          formData.append("appointment_id", viewingAppointment.id);
        } else {
          formData.append("status", "planned");
          formData.append("case_file_id", caseFileId);
        }

        const result = sidebarMode === "edit"
          ? await updateCaseFileAppointment(formData)
          : await createCaseFileAppointment(formData);

        if (result.success) {
          setSuccess(sidebarMode === "edit" ? "Appointment updated successfully!" : "Appointment created successfully!");
          // Reset form
          setAppointmentForm({
            title: "",
            description: "",
            start_time: "",
            end_time: "",
            service_id: "",
            room_id: "",
            participants: [],
          });
          setSelectedDate(new Date());
          // Close sidebar after a brief delay to show success message
          setTimeout(() => {
            setIsSidebarOpen(false);
            setSuccess(null);
            // Refresh the page to show the updated appointment
            window.location.reload();
          }, 1500);
        } else {
          setError(result.error || `Failed to ${sidebarMode === "edit" ? "update" : "create"} appointment`);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      }
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "missed":
        return "bg-orange-100 text-orange-800";
      case "postponed":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex">
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`transition-all duration-500 ease-in-out ${isSidebarOpen ? 'flex-1 mr-6' : 'w-full'}`}
      >
        <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <CardTitle>
                {isSidebarOpen
                  ? sidebarMode === "create"
                    ? "New Appointment Details"
                    : sidebarMode === "edit"
                    ? "Edit Appointment Details"
                    : "Appointment Details"
                  : t.title
                }
              </CardTitle>
            </div>
            {!isSidebarOpen && (
              <Button size="sm" onClick={handleCreateAppointment}>
                <Plus className="h-4 w-4 mr-2" />
                {t.scheduleNew}
              </Button>
            )}
          </div>
          <p className="text-sm text-muted-foreground">
            {isSidebarOpen
              ? sidebarMode === "create"
                ? "Fill in the appointment details below"
                : sidebarMode === "edit"
                ? "Update the appointment details below"
                : "View appointment details and information"
              : t.description
            }
          </p>
        </CardHeader>
        <CardContent>
          {isSidebarOpen ? (
            sidebarMode === "create" || sidebarMode === "edit" ? (
              // Appointment Form (Create/Edit)
              <div className="space-y-4">
              {/* Error/Success Messages */}
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              {success && (
                <Alert className="border-green-200 bg-green-50 text-green-800">
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>{success}</AlertDescription>
                </Alert>
              )}

              <div className="grid gap-3">
                {/* Basic Information */}
                <div>
                  <Label htmlFor="title" className="text-sm font-medium">Appointment Title *</Label>
                  <Input
                    id="title"
                    value={appointmentForm.title}
                    onChange={(e) => handleFormChange("title", e.target.value)}
                    placeholder="Enter appointment title"
                    disabled={isPending}
                    className="mt-1"
                  />
                </div>

                <div>
                  <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                  <Textarea
                    id="description"
                    value={appointmentForm.description}
                    onChange={(e) => handleFormChange("description", e.target.value)}
                    placeholder="Enter appointment description"
                    rows={2}
                    disabled={isPending}
                    className="mt-1 resize-none"
                  />
                </div>

                {/* Service Selection - Pre-populated from Case File */}
                <div>
                  <Label className="text-sm font-medium">Service Type (from Case File)</Label>
                  <div className="mt-1 p-2 bg-muted rounded-md border">
                    <div className="font-medium text-sm">{getServiceName(appointmentForm.service_id)}</div>
                    <div className="text-xs text-muted-foreground">
                      {mockServices.find(s => s.id === appointmentForm.service_id)?.description}
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Service type is automatically set from the case file and cannot be changed.
                  </p>
                </div>

                {/* Participants Selection */}
                <div>
                  <Label className="text-sm font-medium">Participants</Label>
                  <div className="mt-1 space-y-2 max-h-32 overflow-y-auto border rounded-md p-2">
                    {mockParticipants.map((participant) => (
                      <div key={participant.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={`participant-${participant.id}`}
                          checked={appointmentForm.participants.includes(participant.id)}
                          onCheckedChange={() => handleParticipantToggle(participant.id)}
                          disabled={isPending}
                        />
                        <Label
                          htmlFor={`participant-${participant.id}`}
                          className="text-sm flex-1 cursor-pointer"
                        >
                          <span className="font-medium">{participant.name}</span>
                          <span className="text-muted-foreground ml-2">({participant.role})</span>
                        </Label>
                        <Badge variant="outline" className="text-xs">
                          {participant.type}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Time Selection */}
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="start_time" className="text-sm font-medium">Start Time *</Label>
                    <Input
                      id="start_time"
                      type="time"
                      value={appointmentForm.start_time}
                      onChange={(e) => handleFormChange("start_time", e.target.value)}
                      disabled={isPending}
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="end_time" className="text-sm font-medium">End Time *</Label>
                    <Input
                      id="end_time"
                      type="time"
                      value={appointmentForm.end_time}
                      onChange={(e) => handleFormChange("end_time", e.target.value)}
                      disabled={isPending}
                      className="mt-1"
                    />
                  </div>
                </div>

                {/* Room Selection */}
                <div>
                  <Label className="text-sm font-medium">Room</Label>
                  <Select
                    value={appointmentForm.room_id}
                    onValueChange={(value) => handleFormChange("room_id", value)}
                    disabled={isPending}
                  >
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Select a room" />
                    </SelectTrigger>
                    <SelectContent>
                      {mockRooms.map((room) => (
                        <SelectItem key={room.id} value={room.id}>
                          <div>
                            <div className="font-medium">{room.name}</div>
                            <div className="text-xs text-muted-foreground">
                              Capacity: {room.capacity} • {room.features.join(", ")}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex justify-end gap-2 pt-3 border-t">
                <Button
                  variant="outline"
                  onClick={() => setIsSidebarOpen(false)}
                  disabled={isPending}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSaveAppointment}
                  disabled={isPending}
                >
                  {isPending ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      {sidebarMode === "edit" ? "Updating..." : "Creating..."}
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      {sidebarMode === "edit" ? "Update Appointment" : "Save Appointment"}
                    </>
                  )}
                </Button>
              </div>
            </div>
            ) : (
              // Appointment Details View
              <div className="space-y-4">
                {viewingAppointment && (
                  <>
                    {/* Service Type as Header */}
                    <div className="pb-2 border-b">
                      <Badge variant="secondary" className="mb-2">
                        {getServiceName("1")} {/* Mock: from case file */}
                      </Badge>
                      <h3 className="text-lg font-medium">{viewingAppointment.title}</h3>
                      {viewingAppointment.description && (
                        <p className="text-sm text-muted-foreground mt-1">{viewingAppointment.description}</p>
                      )}
                    </div>

                    {/* Participants */}
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Participants</Label>
                      <div className="mt-1 space-y-1">
                        {["1", "3", "5"].map((participantId) => { // Mock data
                          const participant = mockParticipants.find(p => p.id === participantId);
                          return participant ? (
                            <div key={participantId} className="flex items-center justify-between text-sm">
                              <span className="font-medium">{participant.name}</span>
                              <div className="flex items-center gap-2">
                                <span className="text-muted-foreground">({participant.role})</span>
                                <Badge variant="outline" className="text-xs">
                                  {participant.type}
                                </Badge>
                              </div>
                            </div>
                          ) : null;
                        })}
                      </div>
                    </div>

                    {/* Date and Time */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Date</Label>
                        <p className="text-sm font-medium">{viewingAppointment.date_display}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Status</Label>
                        <Badge className={getStatusColor(viewingAppointment.status)}>
                          {viewingAppointment.status_display || viewingAppointment.status}
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Start Time</Label>
                        <p className="text-sm font-medium">{viewingAppointment.start_time}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">End Time</Label>
                        <p className="text-sm font-medium">{viewingAppointment.end_time}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-muted-foreground">Duration</Label>
                        <p className="text-sm font-medium">{viewingAppointment.duration_display}</p>
                      </div>
                    </div>

                    {/* Room Information */}
                    <div>
                      <Label className="text-sm font-medium text-muted-foreground">Room</Label>
                      <div className="mt-1 p-2 bg-muted rounded-md">
                        <div className="font-medium text-sm">{getRoomName("2")}</div> {/* Mock data */}
                        <div className="text-xs text-muted-foreground">
                          {mockRooms.find(r => r.id === "2")?.features.join(" • ")}
                        </div>
                      </div>
                    </div>
                  </>
                )}
              </div>

                    <div className="flex justify-end gap-2 pt-3 border-t">
                      <Button variant="outline" onClick={() => setIsSidebarOpen(false)}>
                        Close
                      </Button>
                      <Button onClick={() => handleEditAppointment(viewingAppointment)}>
                        <Save className="h-4 w-4 mr-2" />
                        Edit Appointment
                      </Button>
                    </div>
                  </>
                )}
              </div>
            )
          ) : appointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">{t.noAppointments}</h3>
              <p className="text-muted-foreground mb-4">{t.noAppointmentsDescription}</p>
              <Button onClick={handleCreateAppointment}>
                <Plus className="h-4 w-4 mr-2" />
                {t.scheduleNew}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {appointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{appointment.title}</h4>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status_display || appointment.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {appointment.date_display}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {appointment.time_display}
                      </div>
                      <div>{appointment.duration_display}</div>
                    </div>
                    {appointment.description && (
                      <p className="text-sm text-muted-foreground mt-2">
                        {appointment.description}
                      </p>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewAppointment(appointment)}
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    {t.viewAppointment}
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
        </Card>
      </motion.div>

      {/* Secondary Sidebar for Appointment Creation */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            initial={{ x: "100%" }}
            animate={{ x: 0 }}
            exit={{ x: "100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 200 }}
            className="w-96 bg-background border-l shadow-lg flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">
                {sidebarMode === "create"
                  ? "Select Date"
                  : sidebarMode === "edit"
                  ? "Change Date"
                  : "Appointment Date"
                }
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Calendar Content */}
            <div className="flex-1 p-3 overflow-y-auto">
              <div className="space-y-3">
                <div className="text-xs text-muted-foreground">
                  {sidebarMode === "create"
                    ? "Select a date for your appointment"
                    : sidebarMode === "edit"
                    ? "Change the appointment date"
                    : "Appointment scheduled for this date"
                  }
                </div>

                <CalendarComponent
                  mode="single"
                  selected={selectedDate}
                  onSelect={sidebarMode === "create" || sidebarMode === "edit" ? handleDateSelect : undefined}
                  className="rounded-md border w-full"
                  disabled={sidebarMode === "view"}
                />

                {selectedDate && (
                  <div className="p-2 bg-muted rounded text-center">
                    <p className="text-xs font-medium">
                      {sidebarMode === "create"
                        ? "Selected:"
                        : sidebarMode === "edit"
                        ? "New Date:"
                        : "Scheduled:"
                      }
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {selectedDate.toLocaleDateString()}
                    </p>
                  </div>
                )}

                <div className="text-xs text-muted-foreground text-center">
                  {sidebarMode === "create"
                    ? "💡 Pick a date, then fill the form"
                    : sidebarMode === "edit"
                    ? "✏️ Select new date or keep current"
                    : "📅 Appointment date shown above"
                  }
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
