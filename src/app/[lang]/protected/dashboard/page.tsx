import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  FileText,
  Plus,
  AlertCircle,
  CheckCircle,
  Users,
  Bell,
  Activity,
  TrendingUp,
} from "lucide-react";
import { PageHeader } from "@/components";
import { dashboardService } from "./lib/services/DashboardService";
import Link from "next/link";

interface DashboardPageProps {
  params: Promise<{ lang: string }>;
}

export default async function DashboardPage({ params }: DashboardPageProps) {
  const { lang } = await params;

  // Fetch real appointment data
  const appointmentsResult = await dashboardService.getTodayAppointments();
  const todayAppointments = appointmentsResult.success ? appointmentsResult.data || [] : [];

  // Fetch workload summary
  const workloadResult = await dashboardService.getWorkloadSummary();
  const workloadStats = workloadResult.success && workloadResult.data ? workloadResult.data : {
    activeCases: 0,
    pendingRequests: 0,
    upcomingAppointments: 0,
    overdueItems: 0
  };

  // Fetch urgent tasks
  const urgentTasksResult = await dashboardService.getUrgentTasks();
  const urgentTasks = urgentTasksResult.success ? urgentTasksResult.data || [] : [];

  // Fetch recent case files
  const recentCaseFilesResult = await dashboardService.getRecentCaseFiles();
  const recentCaseFiles = recentCaseFilesResult.success ? recentCaseFilesResult.data || [] : [];

  // Fetch recent notifications
  const notificationsResult = await dashboardService.getRecentNotifications();
  const recentNotifications = notificationsResult.success ? notificationsResult.data || [] : [];

  return (
    <div className="space-y-6">
      <PageHeader
        title={lang === "fr" ? "Tableau de bord" : "Dashboard"}
        description={
          lang === "fr" ? "Vue d'ensemble de vos activités" : "Overview of your activities"
        }
      />

      {/* Top Row - Priority Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* My Appointments Today */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Mes rendez-vous aujourd'hui" : "My Appointments Today"}
            </CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{todayAppointments.length}</div>
            <p className="text-xs text-muted-foreground">
              {lang === "fr" ? "rendez-vous planifiés" : "appointments scheduled"}
            </p>
            <div className="mt-4 space-y-2">
              {todayAppointments.length > 0 ? (
                todayAppointments.map((appointment) => (
                  <div
                    key={appointment.id}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex-1">
                      <p className="text-sm font-medium">{appointment.time}</p>
                      <p className="text-xs text-muted-foreground truncate">{appointment.title}</p>
                      {appointment.room && (
                        <p className="text-xs text-muted-foreground">{appointment.room}</p>
                      )}
                    </div>
                    <Badge
                      variant={appointment.status === "confirmed" ? "default" : "secondary"}
                      className="text-xs"
                    >
                      {appointment.status === "confirmed"
                        ? lang === "fr"
                          ? "Confirmé"
                          : "Confirmed"
                        : lang === "fr"
                          ? "Planifié"
                          : "Planned"}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {lang === "fr"
                    ? "Aucun rendez-vous aujourd'hui"
                    : "No appointments today"}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/scheduling/appointments`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <Calendar className="h-4 w-4 mr-2" />
                {lang === "fr" ? "Voir tous" : "View All"}
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Urgent Tasks */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Tâches urgentes" : "Urgent Tasks"}
            </CardTitle>
            <AlertCircle className="h-4 w-4 text-destructive" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-destructive">{urgentTasks.length}</div>
            <p className="text-xs text-muted-foreground">
              {lang === "fr" ? "éléments nécessitent une attention" : "items need attention"}
            </p>
            <div className="mt-4 space-y-2">
              {urgentTasks.length > 0 ? (
                urgentTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-center justify-between p-2 bg-muted/50 rounded-md"
                  >
                    <div className="flex-1">
                      <p className="text-sm font-medium">{task.title}</p>
                      <p className="text-xs text-muted-foreground">{task.dueDate}</p>
                    </div>
                    <Badge
                      variant={task.priority === "high" ? "destructive" : "secondary"}
                      className="text-xs"
                    >
                      {task.priority === "high"
                        ? lang === "fr"
                          ? "Urgent"
                          : "High"
                        : lang === "fr"
                          ? "Moyen"
                          : "Medium"}
                    </Badge>
                  </div>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {lang === "fr"
                    ? "Aucune tâche urgente"
                    : "No urgent tasks"}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Actions rapides" : "Quick Actions"}
            </CardTitle>
            <Plus className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Link href={`/${lang}/protected/scheduling/appointments/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Calendar className="h-4 w-4 mr-2" />
                  {lang === "fr" ? "Planifier un rendez-vous" : "Schedule Appointment"}
                </Button>
              </Link>
              <Link href={`/${lang}/protected/case-file/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <FileText className="h-4 w-4 mr-2" />
                  {lang === "fr" ? "Créer un dossier" : "Create Case File"}
                </Button>
              </Link>
              <Link href={`/${lang}/protected/automation/wizard/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Plus className="h-4 w-4 mr-2" />
                  {lang === "fr" ? "Nouvelle demande" : "New Request"}
                </Button>
              </Link>
              <Link href={`/${lang}/protected/contact/management/create`}>
                <Button variant="outline" size="sm" className="w-full justify-start">
                  <Users className="h-4 w-4 mr-2" />
                  {lang === "fr" ? "Ajouter un contact" : "Add Contact"}
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Second Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Recent Case Files */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Dossiers récents" : "Recent Case Files"}
            </CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentCaseFiles.length > 0 ? (
                recentCaseFiles.map((caseFile) => (
                  <Link
                    key={caseFile.id}
                    href={`/${lang}/protected/case-file/${caseFile.id}/active`}
                    className="block"
                  >
                    <div className="flex items-center justify-between p-2 bg-muted/50 rounded-md hover:bg-muted/70 transition-colors">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{caseFile.case_number}</p>
                        <p className="text-xs text-muted-foreground">{caseFile.title}</p>
                        <p className="text-xs text-muted-foreground">{caseFile.lastUpdate}</p>
                      </div>
                      <Badge
                        variant={caseFile.status === "active" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {caseFile.status === "active"
                          ? lang === "fr"
                            ? "Actif"
                            : "Active"
                          : lang === "fr"
                            ? "En attente"
                            : "Pending"}
                      </Badge>
                    </div>
                  </Link>
                ))
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {lang === "fr"
                    ? "Aucun dossier récent"
                    : "No recent case files"}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/case-file`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <FileText className="h-4 w-4 mr-2" />
                {lang === "fr" ? "Voir tous les dossiers" : "View All Cases"}
              </Button>
            </Link>
          </CardContent>
        </Card>

        {/* Workload Summary */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Résumé de la charge de travail" : "Workload Summary"}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {lang === "fr" ? "Dossiers actifs" : "Active Cases"}
                </span>
                <span className="text-sm font-medium">{workloadStats.activeCases}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {lang === "fr" ? "Demandes en attente" : "Pending Requests"}
                </span>
                <span className="text-sm font-medium">{workloadStats.pendingRequests}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {lang === "fr" ? "Rendez-vous à venir" : "Upcoming Appointments"}
                </span>
                <span className="text-sm font-medium">{workloadStats.upcomingAppointments}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {lang === "fr" ? "Éléments en retard" : "Overdue Items"}
                </span>
                <span className="text-sm font-medium text-destructive">
                  {workloadStats.overdueItems}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {lang === "fr" ? "Notifications" : "Notifications"}
            </CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentNotifications.length > 0 ? (
                recentNotifications.map((notification) => {
                  const IconComponent = notification.type === 'success' ? CheckCircle :
                                      notification.type === 'warning' ? AlertCircle :
                                      notification.type === 'error' ? AlertCircle :
                                      Activity;

                  const iconColor = notification.type === 'success' ? 'text-green-600' :
                                  notification.type === 'warning' ? 'text-orange-600' :
                                  notification.type === 'error' ? 'text-red-600' :
                                  'text-blue-600';

                  return (
                    <div key={notification.id} className="flex items-start space-x-2 p-2 bg-muted/50 rounded-md">
                      <IconComponent className={`h-4 w-4 ${iconColor} mt-0.5`} />
                      <div className="flex-1">
                        <p className="text-sm font-medium">{notification.title}</p>
                        <p className="text-xs text-muted-foreground">{notification.timestamp}</p>
                      </div>
                    </div>
                  );
                })
              ) : (
                <div className="text-center py-4 text-sm text-muted-foreground">
                  {lang === "fr"
                    ? "Aucune notification récente"
                    : "No recent notifications"}
                </div>
              )}
            </div>
            <Link href={`/${lang}/protected/notifications`}>
              <Button variant="outline" size="sm" className="w-full mt-3">
                <Bell className="h-4 w-4 mr-2" />
                {lang === "fr" ? "Voir toutes" : "View All"}
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
