"use server";

import { auth } from "@/lib/authentication/services/AuthenticationService";
import { AppointmentService } from "../../scheduling/appointments/lib/services/AppointmentService";
import { AppointmentWithDetails } from "../../scheduling/appointments/lib/types";

/**
 * Get appointments for a specific case file
 */
export async function getCaseFileAppointments(caseFileId: string): Promise<{
  success: boolean;
  data?: AppointmentWithDetails[];
  error?: any;
  message: string;
}> {
  try {
    // Validate authentication
    const currentUser = await auth.getCurrentUser();
    if (!currentUser) {
      return {
        success: false,
        message: "Authentication required",
      };
    }

    const userProfile = await auth.getCurrentUserProfile();
    if (!userProfile?.organizationId) {
      return {
        success: false,
        message: "Organization context required",
      };
    }

    // Validate case file ID
    if (!caseFileId) {
      return {
        success: false,
        message: "Case file ID is required",
      };
    }

    // Get appointments using service with case_file_id filter
    const appointmentService = new AppointmentService();
    const result = await appointmentService.listAppointments(userProfile.organizationId, {
      limit: 100, // Get all appointments for the case file
    });

    if (!result.success) {
      return {
        success: false,
        error: result.error,
        message: result.message,
      };
    }

    // Filter appointments by case_file_id (since the service doesn't support this filter yet)
    const allAppointments = result.data?.appointments || [];
    const caseFileAppointments = allAppointments.filter(
      (appointment) => appointment.case_file_id === caseFileId
    );

    return {
      success: true,
      data: caseFileAppointments,
      message: "Case file appointments fetched successfully",
    };
  } catch (error) {
    return {
      success: false,
      error,
      message: error instanceof Error ? error.message : "Failed to fetch case file appointments",
    };
  }
}
