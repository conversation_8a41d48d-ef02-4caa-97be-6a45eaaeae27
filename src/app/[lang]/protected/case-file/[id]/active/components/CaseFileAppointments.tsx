"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, Eye, Plus, X } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AppointmentWithDetails } from "../../../../scheduling/appointments/lib/types";
import Link from "next/link";

interface CaseFileAppointmentsProps {
  caseFileId: string;
  appointments: AppointmentWithDetails[];
  lang: string;
  dictionary?: {
    title: string;
    description: string;
    noAppointments: string;
    noAppointmentsDescription: string;
    viewAppointment: string;
    scheduleNew: string;
  };
}

const defaultDictionary = {
  title: "Case File Appointments",
  description: "Appointments associated with this case file",
  noAppointments: "No appointments found",
  noAppointmentsDescription: "No appointments have been scheduled for this case file yet",
  viewAppointment: "View Appointment",
  scheduleNew: "Schedule New Appointment",
};

export function CaseFileAppointments({
  caseFileId: _caseFileId,
  appointments,
  lang,
  dictionary,
}: CaseFileAppointmentsProps) {
  const t = dictionary || defaultDictionary;
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "planned":
        return "bg-blue-100 text-blue-800";
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "in_progress":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-emerald-100 text-emerald-800";
      case "missed":
        return "bg-orange-100 text-orange-800";
      case "postponed":
        return "bg-purple-100 text-purple-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="flex gap-6">
      {/* Main Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className={`transition-all duration-300 ${isSidebarOpen ? 'flex-1' : 'w-full'}`}
      >
        <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              <CardTitle>{t.title}</CardTitle>
            </div>
            <Button size="sm" onClick={() => setIsSidebarOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              {t.scheduleNew}
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">{t.description}</p>
        </CardHeader>
        <CardContent>
          {appointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">{t.noAppointments}</h3>
              <p className="text-muted-foreground mb-4">{t.noAppointmentsDescription}</p>
              <Button onClick={() => setIsSidebarOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                {t.scheduleNew}
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {appointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium">{appointment.title}</h4>
                      <Badge className={getStatusColor(appointment.status)}>
                        {appointment.status_display || appointment.status}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-4 w-4" />
                        {appointment.date_display}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {appointment.time_display}
                      </div>
                      <div>{appointment.duration_display}</div>
                    </div>
                    {appointment.description && (
                      <p className="text-sm text-muted-foreground mt-2">
                        {appointment.description}
                      </p>
                    )}
                  </div>
                  <Button variant="outline" size="sm" asChild>
                    <Link href={`/${lang}/protected/scheduling/appointments/${appointment.id}`}>
                      <Eye className="h-4 w-4 mr-2" />
                      {t.viewAppointment}
                    </Link>
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
        </Card>
      </motion.div>

      {/* Secondary Sidebar for Appointment Creation */}
      <AnimatePresence>
        {isSidebarOpen && (
          <motion.div
            initial={{ width: 0, opacity: 0 }}
            animate={{ width: 384, opacity: 1 }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ type: "spring", damping: 30, stiffness: 300 }}
            className="bg-background border-l shadow-lg flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">Schedule New Appointment</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsSidebarOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 p-4 overflow-y-auto">
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  Appointment creation form will go here...
                </p>
                <div className="p-4 bg-muted rounded-lg">
                  <p className="text-sm">
                    🎯 This is a proper sidebar!<br/>
                    - Slides from left<br/>
                    - Pushes content to the right<br/>
                    - No backdrop overlay<br/>
                    - Two-panel layout
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
