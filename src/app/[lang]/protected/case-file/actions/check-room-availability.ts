"use server";

import { createClient } from "@/lib/supabase/server";

export async function checkRoomAvailabilityAction(
  roomId: string,
  date: string,
  startTime: string,
  endTime: string,
  organizationId: string,
  excludeAppointmentId?: string // Optional: exclude this appointment from conflict checking (for editing)
): Promise<{ success: boolean; available: boolean; error?: string; conflictDetails?: string }> {
  try {
    const supabase = await createClient();
    const dayOfWeek = new Date(date).getDay();
    const requestedStart = new Date(`${date}T${startTime}`);
    const requestedEnd = new Date(`${date}T${endTime}`);

    // Step 1: Check if room exists and is active
    const { data: room, error: roomError } = await supabase
      .from("rooms")
      .select("*")
      .eq("id", roomId)
      .eq("organization_id", organizationId)
      .single();

    if (roomError || !room) {
      return {
        success: true,
        available: false,
        error: "Room not found",
      };
    }

    if (room.status !== "active") {
      return {
        success: true,
        available: false,
        error: `Room is ${room.status}`,
        conflictDetails: room.status === "maintenance" ? "Room is under maintenance" : "Room is inactive",
      };
    }

    // Step 2: Check regular room availability schedule
    const { data: regularAvailability, error: availabilityError } = await supabase
      .from("room_availability")
      .select("*")
      .eq("room_id", roomId)
      .eq("organization_id", organizationId)
      .eq("day_of_week", dayOfWeek)
      .lte("start_time", startTime)
      .gte("end_time", endTime);

    if (availabilityError) {
      console.warn("Failed to check regular room availability:", availabilityError);
    }

    const hasRegularAvailability = regularAvailability && regularAvailability.length > 0;

    // Step 3: Check room availability exceptions for this specific date
    const { data: exceptions, error: exceptionsError } = await supabase
      .from("room_availability_exceptions")
      .select("*")
      .eq("room_id", roomId)
      .eq("organization_id", organizationId)
      .eq("exception_date", date);

    if (exceptionsError) {
      console.warn("Failed to check room availability exceptions:", exceptionsError);
    }

    // Check if there are exceptions that affect this time slot
    const relevantExceptions = exceptions?.filter(exception => {
      const exceptionStart = new Date(`${date}T${exception.start_time}`);
      const exceptionEnd = new Date(`${date}T${exception.end_time}`);
      
      // Check if the requested time overlaps with the exception
      return (requestedStart < exceptionEnd && requestedEnd > exceptionStart);
    }) || [];

    // If there are exceptions, they override regular availability
    if (relevantExceptions.length > 0) {
      const hasAvailableException = relevantExceptions.some(exception => exception.is_available);
      if (!hasAvailableException) {
        const exception = relevantExceptions[0];
        return {
          success: true,
          available: false,
          error: "Room is not available due to schedule exception",
          conflictDetails: exception.reason ? `Reason: ${exception.reason}` : undefined,
        };
      }
    } else if (!hasRegularAvailability) {
      // No exceptions and no regular availability
      return {
        success: true,
        available: false,
        error: "Room is not available according to its regular schedule",
      };
    }

    // Step 4: Check for existing room bookings/conflicts
    const { data: roomBookings, error: bookingsError } = await supabase
      .from("appointment_rooms")
      .select(`
        id,
        appointments!appointment_rooms_appointment_id_fkey(
          id,
          title,
          appointment_date,
          start_time,
          end_time
        )
      `)
      .eq("room_id", roomId)
      .eq("organization_id", organizationId);

    if (bookingsError) {
      console.warn("Failed to check room bookings:", bookingsError);
      // If we can't check conflicts, allow the booking but warn
      return {
        success: true,
        available: true,
        error: "Could not verify room conflicts, but allowing booking",
      };
    }

    // Check for time conflicts using the already declared variables
    const conflicts = roomBookings?.filter((booking: any) => {
      const appointment = booking.appointments;
      if (!appointment) return false;

      // Skip the current appointment if we're editing
      if (excludeAppointmentId && appointment.id === excludeAppointmentId) {
        return false;
      }

      // Only check appointments on the same date
      if (appointment.appointment_date !== date) {
        return false;
      }

      const appointmentStart = new Date(`${appointment.appointment_date}T${appointment.start_time}`);
      const appointmentEnd = new Date(`${appointment.appointment_date}T${appointment.end_time}`);

      // Check for time overlap
      return (
        (requestedStart < appointmentEnd && requestedEnd > appointmentStart)
      );
    }) || [];

    if (conflicts.length > 0) {
      const conflictingAppointment = conflicts[0].appointments;
      return {
        success: true,
        available: false,
        error: "Room has a conflicting booking",
        conflictDetails: `Conflicts with "${conflictingAppointment.title}" from ${conflictingAppointment.start_time} to ${conflictingAppointment.end_time}`,
      };
    }

    // No conflicts found
    return {
      success: true,
      available: true,
    };

  } catch (error) {
    return {
      success: false,
      available: false,
      error: error instanceof Error ? error.message : "Failed to check room availability",
    };
  }
}
